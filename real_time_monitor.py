#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实时监控模块
提供交易系统的实时监控、风险评估和智能预警功能
"""

import time
import logging
import json
import os
from datetime import datetime, timedelta
from dataclasses import dataclass
from typing import Dict, List, Optional
import threading

@dataclass
class PositionHealth:
    """持仓健康状态"""
    symbol: str
    pnl_percentage: float
    holding_time: float
    risk_level: str  # 'low', 'medium', 'high', 'critical'
    trend_score: float
    volatility_score: float
    recommendation: str  # 'hold', 'reduce', 'close', 'monitor'

class RealTimeMonitor:
    """实时监控器"""
    
    def __init__(self, config: dict):
        self.config = config
        self.monitoring_config = config.get('intelligent_monitoring', {})
        self.optimization_config = config.get('advanced_optimization', {})
        
        # 加载监控配置文件
        self._load_monitor_config()
        
        # 监控状态
        self.is_monitoring = False
        self.monitor_thread = None
        
        # 数据存储
        self.position_history = {}
        self.performance_metrics = {
            'total_trades': 0,
            'profitable_trades': 0,
            'total_pnl': 0.0,
            'max_drawdown': 0.0,
            'win_rate': 0.0,
            'avg_profit': 0.0,
            'avg_loss': 0.0
        }
        
        # 风险指标
        self.risk_metrics = {
            'portfolio_risk': 0.0,
            'correlation_risk': 0.0,
            'concentration_risk': 0.0,
            'volatility_risk': 0.0
        }
        
        # 预警阈值（可动态调整）
        self.alert_thresholds = {
            'portfolio_risk': 0.8,
            'position_loss': -0.05,  # 5%亏损预警
            'volatility': 0.03,      # 3%波动率预警
            'concentration': 0.4      # 40%集中度预警
        }
        
        # 动态调整参数
        self.adaptive_settings = {
            'market_volatility_adjustment': True,  # 根据市场波动调整阈值
            'performance_based_adjustment': True,  # 根据历史表现调整
            'risk_sensitivity': 1.0  # 风险敏感度系数（0.5-2.0）
        }
        
        self.loss_alert_threshold = self.monitoring_config.get('loss_alert_threshold', -0.02)
        self.profit_target_threshold = self.monitoring_config.get('profit_target_threshold', 0.015)
        
        logging.info("实时监控器初始化完成")
    
    def _load_monitor_config(self):
        """加载监控配置文件"""
        try:
            config_path = 'monitor_config.json'
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                # 更新监控设置
                monitoring_settings = config.get('monitoring_settings', {})
                self.monitoring_interval = monitoring_settings.get('monitoring_interval', 30)
                
                # 更新预警阈值
                config_thresholds = config.get('alert_thresholds', {})
                self.alert_thresholds.update(config_thresholds)
                
                # 更新自适应设置
                config_adaptive = config.get('adaptive_settings', {})
                self.adaptive_settings.update(config_adaptive)
                
                # 加载优化功能设置
                self.optimization_features = config.get('optimization_features', {
                    'smart_close_optimization': True,
                    'smart_open_optimization': True,
                    'dynamic_stop_optimization': True,
                    'performance_reporting': True
                })
                
                # 加载风险等级设置
                self.risk_level_config = config.get('risk_levels', {})
                
                # 加载动态止损设置
                self.dynamic_stop_config = config.get('dynamic_stop_loss', {})
                
                # 加载智能开仓设置
                self.smart_opening_config = config.get('smart_opening', {})
                
                logging.info(f"监控配置文件加载成功: {config_path}")
            else:
                logging.warning(f"监控配置文件不存在: {config_path}，使用默认设置")
                # 设置默认值
                self.optimization_features = {
                    'smart_close_optimization': True,
                    'smart_open_optimization': True,
                    'dynamic_stop_optimization': True,
                    'performance_reporting': True
                }
                self.risk_level_config = {}
                self.dynamic_stop_config = {}
                self.smart_opening_config = {}
                
        except Exception as e:
            # 增强配置加载错误处理
            error_type = type(e).__name__
            logging.error(f"加载监控配置文件失败 ({error_type}): {e}，使用默认设置")
            
            # 尝试备份配置恢复
            backup_config_path = 'monitor_config.json.backup'
            if os.path.exists(backup_config_path):
                try:
                    with open(backup_config_path, 'r', encoding='utf-8') as f:
                        backup_config = json.load(f)
                    logging.info("成功从备份配置文件恢复设置")
                    # 应用备份配置
                    self.optimization_features = backup_config.get('optimization_features', {})
                    self.risk_level_config = backup_config.get('risk_levels', {})
                    self.dynamic_stop_config = backup_config.get('dynamic_stop_loss', {})
                    self.smart_opening_config = backup_config.get('smart_opening', {})
                    return
                except Exception as backup_error:
                    logging.warning(f"备份配置文件也无法加载: {backup_error}")
            
            # 设置安全默认值
            self.optimization_features = {
                'smart_close_optimization': True,
                'smart_open_optimization': True,
                'dynamic_stop_optimization': True,
                'performance_reporting': True
            }
            self.risk_level_config = {
                'high_risk': {'loss_pct': -0.03, 'volatility': 0.05, 'hold_time': 3600},
                'medium_risk': {'loss_pct': -0.02, 'volatility': 0.03, 'hold_time': 1800},
                'low_risk': {'loss_pct': -0.01, 'volatility': 0.02, 'hold_time': 900}
            }
            self.dynamic_stop_config = {
                'enable': True,
                'base_stop_loss': -0.01,
                'max_stop_loss': -0.05,
                'adjustment_factor': 0.5
            }
            self.smart_opening_config = {
                'enable': True,
                'risk_threshold': 0.7,
                'max_positions': 8
            }
    
    def _adjust_adaptive_thresholds(self):
        """根据市场情况和历史表现自适应调整阈值"""
        try:
            if not self.adaptive_settings.get('market_volatility_adjustment', False):
                return
            
            # 获取市场风险等级
            market_risk = self._assess_market_risk()
            risk_sensitivity = self.adaptive_settings.get('risk_sensitivity', 1.0)
            
            # 根据市场风险调整阈值
            if market_risk == 'high':
                # 高风险市场：降低风险容忍度
                adjustment_factor = 0.7 * risk_sensitivity
            elif market_risk == 'medium':
                # 中等风险市场：正常阈值
                adjustment_factor = 1.0 * risk_sensitivity
            else:
                # 低风险市场：提高风险容忍度
                adjustment_factor = 1.3 * risk_sensitivity
            
            # 调整预警阈值
            base_thresholds = {
                'portfolio_risk': 0.8,
                'position_loss': -0.05,
                'volatility': 0.03,
                'concentration': 0.4
            }
            
            for key, base_value in base_thresholds.items():
                if key == 'position_loss':
                    # 亏损阈值：风险高时更严格（绝对值更小）
                    self.alert_thresholds[key] = base_value / adjustment_factor
                else:
                    # 其他阈值：风险高时更严格（数值更小）
                    self.alert_thresholds[key] = base_value * adjustment_factor
            
            # 根据历史表现进一步调整
            if self.adaptive_settings.get('performance_based_adjustment', False):
                self._adjust_thresholds_by_performance()
            
            logging.debug(f"自适应阈值调整完成，市场风险: {market_risk}, 调整系数: {adjustment_factor:.2f}")
            
        except Exception as e:
            logging.error(f"自适应阈值调整失败: {e}")
    
    def _adjust_thresholds_by_performance(self):
        """根据历史表现调整阈值"""
        try:
            if not self.position_history:
                return
            
            # 计算最近的整体表现
            recent_performance = []
            for data in list(self.position_history.values())[-20:]:  # 最近20个持仓
                if isinstance(data, dict) and 'current_pnl_percent' in data:
                    recent_performance.append(data['current_pnl_percent'])
            
            if not recent_performance:
                return
            
            avg_performance = sum(recent_performance) / len(recent_performance)
            
            # 如果最近表现不佳，收紧风险控制
            if avg_performance < -0.02:  # 平均亏损超过2%
                performance_factor = 0.8  # 收紧20%
            elif avg_performance > 0.02:  # 平均盈利超过2%
                performance_factor = 1.2  # 放宽20%
            else:
                performance_factor = 1.0  # 保持不变
            
            # 应用表现调整
            self.alert_thresholds['position_loss'] *= performance_factor
            self.alert_thresholds['portfolio_risk'] *= performance_factor
            
            logging.debug(f"基于表现的阈值调整完成，平均表现: {avg_performance*100:.2f}%, 调整系数: {performance_factor:.2f}")
            
        except Exception as e:
            logging.error(f"基于表现的阈值调整失败: {e}")

    def start_monitoring(self, exchange, trade_status_details):
        """启动实时监控"""
        if self.is_monitoring:
            logging.warning("监控已在运行中")
            return
        
        self.is_monitoring = True
        self.exchange = exchange
        self.trade_status_details = trade_status_details
        
        self.monitor_thread = threading.Thread(
            target=self._monitoring_loop,
            daemon=True,
            name="RealTimeMonitor"
        )
        self.monitor_thread.start()
        logging.info("实时监控已启动")
    
    def stop_monitoring(self):
        """停止实时监控"""
        self.is_monitoring = False
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=5)
        logging.info("实时监控已停止")
    
    def _monitoring_loop(self):
        """监控主循环"""
        check_interval = self.monitoring_config.get('position_health_check_interval', 60)
        
        while self.is_monitoring:
            try:
                # 每5个周期执行一次自适应阈值调整
                if not hasattr(self, '_threshold_adjustment_counter'):
                    self._threshold_adjustment_counter = 0
                self._threshold_adjustment_counter += 1
                
                if self._threshold_adjustment_counter >= 5:
                    self._adjust_adaptive_thresholds()
                    self._threshold_adjustment_counter = 0
                
                # 执行健康检查
                self._perform_health_check()
                
                # 更新性能指标
                self._update_performance_metrics()
                
                # 风险评估
                self._assess_portfolio_risk()
                
                # 生成预警
                self._generate_alerts()
                
            except Exception as e:
                # 增强监控循环错误处理
                error_type = type(e).__name__
                
                # 初始化错误计数器
                if not hasattr(self, '_monitor_error_count'):
                    self._monitor_error_count = 0
                    self._last_monitor_error_time = 0
                
                self._monitor_error_count += 1
                current_error_time = time.time()
                
                # 错误频率检测
                if current_error_time - self._last_monitor_error_time < 300:  # 5分钟内连续错误
                    error_frequency = "高频"
                    check_interval = min(check_interval * 1.5, 300)  # 增加检查间隔
                else:
                    error_frequency = "正常"
                    self._monitor_error_count = 1  # 重置计数
                
                # 分类错误处理
                if "network" in str(e).lower() or "connection" in str(e).lower():
                    logging.warning(f"监控网络错误({error_frequency}频率,第{self._monitor_error_count}次): {e}")
                elif "api" in str(e).lower() or "rate" in str(e).lower():
                    logging.warning(f"监控API限制({error_frequency}频率,第{self._monitor_error_count}次): {e}")
                else:
                    logging.error(f"监控未知错误({error_frequency}频率,第{self._monitor_error_count}次): {error_type} - {e}")
                    if self._monitor_error_count <= 3:  # 只在前3次记录详细信息
                        import traceback
                        logging.error(traceback.format_exc())
                
                self._last_monitor_error_time = current_error_time
                
                # 错误恢复策略
                if self._monitor_error_count > 20:
                    logging.critical(f"监控循环连续错误超过20次，暂停监控5分钟")
                    time.sleep(300)  # 暂停5分钟
                    self._monitor_error_count = 0  # 重置计数
            
            time.sleep(check_interval)
    
    def _perform_health_check(self):
        """执行持仓健康检查"""
        if not self.exchange or not self.trade_status_details:
            return
        
        try:
            current_positions = self.exchange.fetch_positions()
            active_positions = [pos for pos in current_positions if float(pos.get('contracts', 0)) > 0]
            
            position_health_list = []
            
            for position in active_positions:
                symbol = position['symbol']
                health = self._analyze_position_health(position)
                position_health_list.append(health)
                
                # 记录历史数据 - 使用字典结构以兼容主程序
                if symbol not in self.position_history:
                    self.position_history[symbol] = {
                        'status': 'active',
                        'current_pnl_percent': 0.0,
                        'risk_level': 'medium',
                        'history': []
                    }
                
                # 更新当前状态
                self.position_history[symbol].update({
                    'status': 'active',
                    'current_pnl_percent': health.pnl_percentage * 100,  # 转换为百分比
                    'risk_level': health.risk_level,
                    'last_update': time.time()
                })
                
                # 添加历史记录
                self.position_history[symbol]['history'].append({
                    'timestamp': time.time(),
                    'pnl_percentage': health.pnl_percentage,
                    'risk_level': health.risk_level
                })
                
                # 保持历史数据在合理范围内
                if len(self.position_history[symbol]['history']) > 1000:
                    self.position_history[symbol]['history'] = self.position_history[symbol]['history'][-500:]
            
            # 记录整体健康状态
            self._log_portfolio_health(position_health_list)
            
        except Exception as e:
            logging.error(f"健康检查失败: {e}")
    
    def _analyze_position_health(self, position) -> PositionHealth:
        """分析单个持仓的健康状态"""
        symbol = position['symbol']
        
        try:
            # 获取当前价格
            ticker = self.exchange.fetch_ticker(symbol)
            current_price = ticker['last']
            
            # 计算收益率
            entry_price = float(position.get('entryPrice', 0))
            if entry_price > 0:
                pnl_percentage = (current_price / entry_price - 1)
            else:
                pnl_percentage = 0
            
            # 计算持仓时间
            entry_time = position.get('timestamp', 0) / 1000
            holding_time = time.time() - entry_time if entry_time > 0 else 0
            
            # 计算趋势评分
            trend_score = self._calculate_trend_score(symbol)
            
            # 计算波动率评分
            volatility_score = self._calculate_volatility_score(symbol)
            
            # 确定风险等级
            risk_level = self._determine_risk_level(pnl_percentage, holding_time, volatility_score)
            
            # 生成建议
            recommendation = self._generate_recommendation(pnl_percentage, risk_level, trend_score)
            
            return PositionHealth(
                symbol=symbol,
                pnl_percentage=pnl_percentage,
                holding_time=holding_time,
                risk_level=risk_level,
                trend_score=trend_score,
                volatility_score=volatility_score,
                recommendation=recommendation
            )
            
        except Exception as e:
            logging.error(f"分析持仓 {symbol} 健康状态失败: {e}")
            return PositionHealth(
                symbol=symbol,
                pnl_percentage=0,
                holding_time=0,
                risk_level='unknown',
                trend_score=0,
                volatility_score=0,
                recommendation='monitor'
            )
    
    def _assess_market_risk(self) -> str:
        """评估市场整体风险等级"""
        try:
            # 基于当前持仓的整体表现评估市场风险
            if not self.position_history:
                return 'medium'  # 无历史数据时返回中等风险
            
            # 计算最近持仓的平均表现
            recent_performance = []
            current_time = time.time()
            
            for symbol, pos_data in self.position_history.items():
                 if pos_data and isinstance(pos_data, dict):
                     # 获取最近的记录
                     history = pos_data.get('history', [])
                     recent_records = [record for record in history 
                                     if current_time - record.get('timestamp', 0) < 3600]  # 最近1小时
                     if recent_records:
                         avg_pnl = sum(record.get('pnl_percentage', 0) for record in recent_records) / len(recent_records)
                         recent_performance.append(avg_pnl)
                     elif pos_data.get('current_pnl_percent') is not None:
                         # 如果没有历史记录，使用当前PnL
                         recent_performance.append(pos_data.get('current_pnl_percent', 0) / 100)
            
            if not recent_performance:
                return 'medium'
            
            # 计算整体表现指标
            avg_performance = sum(recent_performance) / len(recent_performance)
            performance_volatility = self._calculate_list_volatility(recent_performance)
            
            # 风险等级判断
            if avg_performance < -0.03 or performance_volatility > 0.05:
                return 'high'
            elif avg_performance < -0.01 or performance_volatility > 0.03:
                return 'medium'
            else:
                return 'low'
                
        except Exception as e:
            logging.error(f"市场风险评估失败: {e}")
            return 'medium'
    
    def _calculate_list_volatility(self, values: List[float]) -> float:
        """计算数值列表的波动率"""
        if len(values) < 2:
            return 0.0
        
        mean_val = sum(values) / len(values)
        variance = sum((x - mean_val) ** 2 for x in values) / len(values)
        return variance ** 0.5
     
    def _calculate_trend_score(self, symbol) -> float:
        """计算趋势评分"""
        try:
            # 获取K线数据
            ohlcv = self.exchange.fetch_ohlcv(symbol, '15m', limit=20)
            if len(ohlcv) < 10:
                return 0
            
            # 计算短期和中期趋势
            recent_prices = [candle[4] for candle in ohlcv[-5:]]  # 最近5根K线
            medium_prices = [candle[4] for candle in ohlcv[-10:]]  # 最近10根K线
            
            short_trend = (recent_prices[-1] / recent_prices[0] - 1) * 100
            medium_trend = (medium_prices[-1] / medium_prices[0] - 1) * 100
            
            # 综合评分
            trend_score = (short_trend * 0.6 + medium_trend * 0.4)
            return max(-10, min(10, trend_score))  # 限制在-10到10之间
            
        except Exception:
            return 0
    
    def _calculate_volatility_score(self, symbol) -> float:
        """计算波动率评分"""
        try:
            # 获取K线数据
            ohlcv = self.exchange.fetch_ohlcv(symbol, '1h', limit=24)
            if len(ohlcv) < 10:
                return 0
            
            # 计算价格变化率
            price_changes = []
            for i in range(1, len(ohlcv)):
                change = (ohlcv[i][4] / ohlcv[i-1][4] - 1) * 100
                price_changes.append(abs(change))
            
            # 计算平均波动率
            avg_volatility = sum(price_changes) / len(price_changes)
            return min(20, avg_volatility)  # 限制最大值为20
            
        except Exception:
            return 0
    
    def _determine_risk_level(self, pnl_percentage: float, holding_time: float, volatility_score: float) -> str:
        """确定风险等级"""
        # 基于多个因素确定风险等级
        risk_score = 0
        
        # 收益率风险
        if pnl_percentage < -0.05:  # 亏损超过5%
            risk_score += 3
        elif pnl_percentage < -0.02:  # 亏损超过2%
            risk_score += 2
        elif pnl_percentage < 0:  # 亏损
            risk_score += 1
        
        # 持仓时间风险
        if holding_time > 7 * 24 * 3600:  # 超过7天
            risk_score += 2
        elif holding_time > 3 * 24 * 3600:  # 超过3天
            risk_score += 1
        
        # 波动率风险
        if volatility_score > 10:
            risk_score += 2
        elif volatility_score > 5:
            risk_score += 1
        
        # 确定等级
        if risk_score >= 5:
            return 'critical'
        elif risk_score >= 3:
            return 'high'
        elif risk_score >= 1:
            return 'medium'
        else:
            return 'low'
    
    def _generate_recommendation(self, pnl_percentage: float, risk_level: str, trend_score: float) -> str:
        """生成操作建议"""
        # 严重亏损立即平仓
        if pnl_percentage < -0.08:  # 亏损超过8%
            return 'close'
        
        # 根据风险等级和趋势生成建议
        if risk_level == 'critical':
            return 'close'
        elif risk_level == 'high':
            if trend_score < -2:  # 下降趋势
                return 'close'
            else:
                return 'reduce'
        elif risk_level == 'medium':
            if pnl_percentage > self.profit_target_threshold:  # 达到盈利目标
                return 'reduce'
            else:
                return 'monitor'
        else:
            return 'hold'
    
    def _update_performance_metrics(self):
        """更新性能指标"""
        try:
            # 这里可以添加更复杂的性能计算逻辑
            # 暂时保持简单实现
            pass
        except Exception as e:
            logging.error(f"更新性能指标失败: {e}")
    
    def _assess_portfolio_risk(self):
        """评估投资组合风险"""
        try:
            # 这里可以添加投资组合风险评估逻辑
            # 包括相关性分析、集中度风险等
            pass
        except Exception as e:
            logging.error(f"风险评估失败: {e}")
    
    def _generate_alerts(self):
        """生成预警信息"""
        try:
            # 检查是否有需要预警的情况
            # 可以发送邮件、短信或其他通知
            pass
        except Exception as e:
            logging.error(f"生成预警失败: {e}")
    
    def _log_portfolio_health(self, position_health_list: List[PositionHealth]):
        """记录投资组合健康状态"""
        if not position_health_list:
            return
        
        # 统计各风险等级的持仓数量
        risk_counts = {'low': 0, 'medium': 0, 'high': 0, 'critical': 0}
        total_pnl = 0
        
        for health in position_health_list:
            if health.risk_level in risk_counts:
                risk_counts[health.risk_level] += 1
            total_pnl += health.pnl_percentage
        
        avg_pnl = total_pnl / len(position_health_list) if position_health_list else 0
        
        logging.info(f"投资组合健康状态 - 总持仓: {len(position_health_list)}, "
                    f"平均收益率: {avg_pnl*100:.2f}%, "
                    f"风险分布: 低({risk_counts['low']}) 中({risk_counts['medium']}) "
                    f"高({risk_counts['high']}) 严重({risk_counts['critical']})")
    
    def get_portfolio_summary(self) -> dict:
        """获取投资组合摘要"""
        return {
            'performance_metrics': self.performance_metrics.copy(),
            'risk_metrics': self.risk_metrics.copy(),
            'monitoring_status': self.is_monitoring,
            'last_update': datetime.now().isoformat()
        }