必须保留的核心文件
一、主程序文件
14、 trading_system_v1.398.py - 主交易系统程序
6、 run_trading_system_v1.398.bat - 程序启动脚本

二、 策略和选择模块
7、 Select_product.py - 产品选择模块
12、Trading_strategy.py - 交易策略模块
10、 strategy_core.py - 策略核心逻辑
11、strategy_performance.py - 策略性能分析
5、real_time_monitor.py - 实时监控模块
8、spot_symbols.py - 现货交易对管理

三、配置文件
1、 config.json - 主配置文件
15、 user_config.json - 用户配置
9、strategy_config.json - 策略配置
3、 monitor_config.json - 监控配置
4、 performance_config.json - 性能配置
2、 encryption_key.key - 加密密钥文件

四、日志文件
13、 trading_system.log - 系统日志（运行时生成）

五、 文档文件
- 安装指南.txt - 安装说明
- 文件目录说明.txt - 目录结构说明





1、trading_system_v1.397.py - 主程序文件
2、Select_product.py（选择品种）
3、Trading_strategy.py（交易策略）
4、spot_symbols.py - 现货交易相关的符号定义
5、run_trading_system_v1.387.bat - 启动新程序的批处理文件
6、config.json - 配置文件
7、encryption_key.key - 加密密钥
8、user_config.json - 用户配置文件
９、关于"策略配置"按钮的说明
### 🎯 "策略配置"按钮的用途
"策略配置"按钮是交易系统的核心管理功能，主要用于：

1. 选品策略配置 ：
   
   - 管理和配置不同的选品策略（如基于ATR和ADX指标的策略）
   - 设置每个策略的参数，如最大选择数量、最小成交量、风险等级等
   - 启用或禁用特定的选品策略
2. 交易策略管理 ：
   
   - 配置交易策略的各种参数
   - 调整风险控制设置
   - 管理策略的优先级和执行顺序
3. 性能监控设置 ：
   
   - 查看各策略的历史表现数据
   - 导出性能报告
   - 清理旧的性能数据
### 🔧 如何使用策略配置
从界面截图可以看到，策略配置界面包含：

- 选品策略配置 ：可以看到三个选品策略选项
- 参数设置 ：每个策略都有对应的参数配置（如最大选择数量、最小成交量、风险等级）
- 启用开关 ：通过复选框控制策略的启用状态
- 操作按钮 ：保存配置、重载配置、导出报告等




