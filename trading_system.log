2025-06-26 18:40:29,234 - root - INFO - <module>:41 - 程序启动，环境变量 sys.frozen = False
2025-06-26 18:40:38,980 - root - INFO - _load_performance_config:2919 - 性能配置加载成功
2025-06-26 18:40:38,988 - root - ERROR - _load_monitor_config:137 - 加载监控配置文件失败 (AttributeError): 'RealTimeMonitor' object has no attribute 'alert_thresholds'，使用默认设置
2025-06-26 18:40:38,989 - root - INFO - __init__:81 - 实时监控器初始化完成
2025-06-26 18:40:38,989 - root - INFO - _init_real_time_monitor:2973 - 实时监控器初始化成功
2025-06-26 18:40:45,159 - root - INFO - load_account:529 - 账号配置加载成功，已加载字段: password, apiKey, secret, passphrase
2025-06-26 18:40:49,338 - root - INFO - get_exchange:627 - 使用模拟盘模式
2025-06-26 18:40:49,366 - root - INFO - get_exchange:643 - 已启用OKX模拟交易模式，设置选项：{'defaultType': 'swap', 'createMarketBuyOrderRequiresPrice': False, 'test': True, 'adjustForTimeDifference': True, 'recvWindow': 10000}
2025-06-26 18:40:49,367 - root - INFO - get_exchange:647 - 尝试为模拟盘账户设置双向持仓模式 (long_short_mode)...
2025-06-26 18:40:51,033 - root - INFO - get_exchange:656 - 调用 ex.set_position_mode('long_short_mode') 响应: {'code': '0', 'data': [{'posMode': 'long_short_mode'}], 'msg': ''}
2025-06-26 18:40:51,035 - root - INFO - get_exchange:659 - 通过 ex.set_position_mode() 成功设置账户为双向持仓模式。
2025-06-26 18:41:07,178 - root - INFO - get_exchange:712 - 模拟盘连接成功，账户余额: 5000.0 USDT
2025-06-26 18:41:07,179 - root - INFO - get_exchange:715 - API连接验证成功
2025-06-26 18:41:14,258 - root - INFO - save_account:466 - 账号和密钥已成功保存
2025-06-26 18:41:20,706 - root - INFO - select_thread:946 - 准备执行智能选择品种...
2025-06-26 18:41:20,706 - root - INFO - fetch_positions:4006 - 确认模拟交易模式已启用
2025-06-26 18:41:26,310 - root - INFO - fetch_positions:4078 - 活跃持仓: BTC/USDT, 类型: spot, 方向: long, 数量: 1.0, 入场价: 107383.6
2025-06-26 18:41:26,312 - root - INFO - fetch_positions:4078 - 活跃持仓: OKB/USDT, 类型: spot, 方向: long, 数量: 100.0, 入场价: 50.11
2025-06-26 18:41:26,313 - root - INFO - fetch_positions:4078 - 活跃持仓: ETH/USDT, 类型: spot, 方向: long, 数量: 1.0, 入场价: 2458.1
2025-06-26 18:41:26,316 - root - INFO - fetch_positions:4083 - 获取持仓: 3个活跃持仓 (合约: 0, 现货: 3)
2025-06-26 18:41:26,317 - root - INFO - select_thread:954 - 当前实际持仓品种: []
2025-06-26 18:41:26,318 - root - INFO - select_thread:968 - 准备调用选品策略，最多选择 5 个新品种。
2025-06-26 18:41:26,321 - strategy_core - ERROR - wrapper:513 - 策略执行失败: select_product.选品策略一, 错误: select_symbols_by_atr_adx() got an unexpected keyword argument 'min_adx'
2025-06-26 18:41:26,324 - root - ERROR - execute_select_product_strategy:1119 - 执行ATR/ADX策略失败: An asyncio.Future, a coroutine or an awaitable is required
2025-06-26 18:41:26,325 - root - INFO - select_thread:992 - 手动智能选品成功，策略: 选品策略一，选择品种: ['BTC/USDT:USDT', 'ETH/USDT:USDT', 'BNB/USDT:USDT']
2025-06-26 18:41:26,326 - root - INFO - select_thread:1008 - 智能模块 '选品策略一' 选择了 3 个品种: ['BTC/USDT:USDT', 'ETH/USDT:USDT', 'BNB/USDT:USDT']
2025-06-26 18:41:26,329 - root - INFO - update_ui_and_start_trading:1036 - 自动交易线程未运行，正在启动...
2025-06-26 18:41:26,331 - root - INFO - auto_trading:1611 - 使用内置交易策略
2025-06-26 18:41:26,333 - root - INFO - trade_loop:1617 - 自动交易循环线程已启动。
2025-06-26 18:41:26,334 - root - INFO - auto_trading:2181 - auto_trading 方法执行完毕，交易线程已启动。
2025-06-26 18:41:26,340 - root - INFO - start_monitoring:275 - 实时监控已启动
2025-06-26 18:41:26,341 - root - INFO - trade_loop:1622 - 实时监控器已启动
2025-06-26 18:41:26,343 - root - INFO - trade_loop:1633 - 本轮选品间隔设置为: 165秒
2025-06-26 18:41:28,749 - root - INFO - trade_loop:1643 - 动态资金分配: 总余额 5000.00 USDT, 单品种分配 448.18 USDT (9.0%)
2025-06-26 18:41:31,539 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:41:31,540 - root - INFO - _init_or_get_position_detail:1460 - SYNC_STATUS: New symbol BTC/USDT found. Initializing in trade_status_details.
2025-06-26 18:41:31,541 - root - INFO - _update_position_entry_info:1477 - SYNC_STATUS: Initialized/Updated entry_time for BTC/USDT to 1750934491.5396655.
2025-06-26 18:41:31,542 - root - INFO - _init_or_get_position_detail:1460 - SYNC_STATUS: New symbol OKB/USDT found. Initializing in trade_status_details.
2025-06-26 18:41:31,543 - root - INFO - _update_position_entry_info:1477 - SYNC_STATUS: Initialized/Updated entry_time for OKB/USDT to 1750934491.5396655.
2025-06-26 18:41:31,544 - root - INFO - _init_or_get_position_detail:1460 - SYNC_STATUS: New symbol ETH/USDT found. Initializing in trade_status_details.
2025-06-26 18:41:31,545 - root - INFO - _update_position_entry_info:1477 - SYNC_STATUS: Initialized/Updated entry_time for ETH/USDT to 1750934491.5396655.
2025-06-26 18:41:31,546 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.006s)
2025-06-26 18:41:31,546 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 3, API增强: 3
2025-06-26 18:41:31,547 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:41:31,548 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:41:37,681 - strategy_core - ERROR - wrapper:513 - 策略执行失败: select_product.选品策略一, 错误: select_symbols_by_atr_adx() got an unexpected keyword argument 'min_adx'
2025-06-26 18:41:37,683 - root - ERROR - execute_select_product_strategy:1119 - 执行ATR/ADX策略失败: An asyncio.Future, a coroutine or an awaitable is required
2025-06-26 18:41:37,684 - root - INFO - trade_loop:1797 - 智能选品策略 选品策略一 选出的可开仓候选: ['BTC/USDT:USDT', 'ETH/USDT:USDT', 'BNB/USDT:USDT']
2025-06-26 18:41:37,685 - root - INFO - trade_loop:1881 - 准备为智能选择的品种 ['BTC/USDT:USDT', 'ETH/USDT:USDT', 'BNB/USDT:USDT'] 进行开仓决策。
2025-06-26 18:41:37,685 - root - INFO - smart_trading_strategy_v2:37 - V2_Strat - 开始为 BTC/USDT:USDT 执行增强智能交易策略，风险等级: medium
2025-06-26 18:41:41,107 - root - ERROR - calculate_enhanced_indicators_v2:659 - 计算增强技术指标失败: name 'high_low' is not defined
2025-06-26 18:41:41,112 - root - ERROR - smart_trading_strategy_v2:199 - V2_Strat - 为 BTC/USDT:USDT 执行策略时发生严重错误: float() argument must be a string or a real number, not 'NoneType'
Traceback (most recent call last):
  File "d:\AI\VSC3\V1.398\Trading_strategy.py", line 161, in smart_trading_strategy_v2
    volume_24h = float(ticker.get('quoteVolume', 0))
TypeError: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 18:41:41,134 - root - INFO - trade_loop:1921 - 为 BTC/USDT:USDT 获取到策略信号: hold, 数量: 0, 价格: None, 原因: 策略执行异常: float() argument must be a string or a real number, not 'NoneType', 策略名: smart_v2_enhanced
2025-06-26 18:41:41,136 - root - INFO - trade_loop:2035 - 策略信号为 hold for BTC/USDT:USDT (原因: 策略执行异常: float() argument must be a string or a real number, not 'NoneType')，不执行开仓操作。
2025-06-26 18:41:41,137 - root - INFO - smart_trading_strategy_v2:37 - V2_Strat - 开始为 ETH/USDT:USDT 执行增强智能交易策略，风险等级: medium
2025-06-26 18:41:48,901 - root - ERROR - calculate_enhanced_indicators_v2:659 - 计算增强技术指标失败: name 'high_low' is not defined
2025-06-26 18:41:48,906 - root - ERROR - smart_trading_strategy_v2:199 - V2_Strat - 为 ETH/USDT:USDT 执行策略时发生严重错误: float() argument must be a string or a real number, not 'NoneType'
Traceback (most recent call last):
  File "d:\AI\VSC3\V1.398\Trading_strategy.py", line 161, in smart_trading_strategy_v2
    volume_24h = float(ticker.get('quoteVolume', 0))
TypeError: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 18:41:48,912 - root - INFO - trade_loop:1921 - 为 ETH/USDT:USDT 获取到策略信号: hold, 数量: 0, 价格: None, 原因: 策略执行异常: float() argument must be a string or a real number, not 'NoneType', 策略名: smart_v2_enhanced
2025-06-26 18:41:48,915 - root - INFO - trade_loop:2035 - 策略信号为 hold for ETH/USDT:USDT (原因: 策略执行异常: float() argument must be a string or a real number, not 'NoneType')，不执行开仓操作。
2025-06-26 18:41:48,922 - root - INFO - smart_trading_strategy_v2:37 - V2_Strat - 开始为 BNB/USDT:USDT 执行增强智能交易策略，风险等级: medium
2025-06-26 18:41:58,124 - root - ERROR - calculate_enhanced_indicators_v2:659 - 计算增强技术指标失败: name 'high_low' is not defined
2025-06-26 18:41:58,128 - root - ERROR - smart_trading_strategy_v2:199 - V2_Strat - 为 BNB/USDT:USDT 执行策略时发生严重错误: float() argument must be a string or a real number, not 'NoneType'
Traceback (most recent call last):
  File "d:\AI\VSC3\V1.398\Trading_strategy.py", line 161, in smart_trading_strategy_v2
    volume_24h = float(ticker.get('quoteVolume', 0))
TypeError: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 18:41:58,130 - root - INFO - trade_loop:1921 - 为 BNB/USDT:USDT 获取到策略信号: hold, 数量: 0, 价格: None, 原因: 策略执行异常: float() argument must be a string or a real number, not 'NoneType', 策略名: smart_v2_enhanced
2025-06-26 18:41:58,131 - root - INFO - trade_loop:2035 - 策略信号为 hold for BNB/USDT:USDT (原因: 策略执行异常: float() argument must be a string or a real number, not 'NoneType')，不执行开仓操作。
2025-06-26 18:41:58,132 - root - INFO - trade_loop:2047 - 所有本轮智能选择的品种均已处理完毕。
2025-06-26 18:42:01,134 - root - INFO - fetch_positions:4006 - 确认模拟交易模式已启用
2025-06-26 18:42:03,181 - root - INFO - update_main_trading_table:2242 - 持仓数量变化: 3 -> 0
2025-06-26 18:42:03,182 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 0, 程序状态数量: 6, 总显示数量: 6
2025-06-26 18:42:06,448 - root - INFO - update_main_trading_table:2309 - 账户余额更新: 5000.00 USDT (缓存60s)
2025-06-26 18:42:06,598 - root - INFO - fetch_positions:4078 - 活跃持仓: BTC/USDT, 类型: spot, 方向: long, 数量: 1.0, 入场价: 107359.2
2025-06-26 18:42:06,599 - root - INFO - fetch_positions:4078 - 活跃持仓: OKB/USDT, 类型: spot, 方向: long, 数量: 100.0, 入场价: 50.09
2025-06-26 18:42:06,599 - root - INFO - fetch_positions:4078 - 活跃持仓: ETH/USDT, 类型: spot, 方向: long, 数量: 1.0, 入场价: 2455.44
2025-06-26 18:42:06,600 - root - INFO - fetch_positions:4083 - 获取持仓: 3个活跃持仓 (合约: 0, 现货: 3)
2025-06-26 18:42:06,601 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:42:06,602 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:42:06,604 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:42:06,605 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:42:06,606 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:42:10,751 - root - INFO - update_main_trading_table:2242 - 持仓数量变化: 3 -> 0
2025-06-26 18:42:10,752 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 0, 程序状态数量: 6, 总显示数量: 6
2025-06-26 18:42:15,787 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:42:15,788 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:42:15,788 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:42:15,789 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:42:15,789 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:42:23,663 - root - INFO - update_main_trading_table:2242 - 持仓数量变化: 3 -> 0
2025-06-26 18:42:23,664 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 0, 程序状态数量: 6, 总显示数量: 6
2025-06-26 18:42:27,271 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:42:27,272 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:42:27,273 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:42:27,273 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:42:27,274 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:42:29,654 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:42:29,654 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:42:30,303 - root - WARNING - update_main_trading_table:2440 - 备用方法获取positions价格失败: okx does not have market symbol positions
2025-06-26 18:42:30,303 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:42:30,305 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:42:30,722 - root - WARNING - update_main_trading_table:2440 - 备用方法获取spot_count价格失败: okx does not have market symbol spot_count
2025-06-26 18:42:30,723 - root - WARNING - update_main_trading_table:2440 - 备用方法获取contract_count价格失败: okx does not have market symbol contract_count
2025-06-26 18:42:34,229 - root - INFO - fetch_positions:4078 - 活跃持仓: BTC/USDT, 类型: spot, 方向: long, 数量: 1.0, 入场价: 107360.1
2025-06-26 18:42:34,229 - root - INFO - fetch_positions:4078 - 活跃持仓: OKB/USDT, 类型: spot, 方向: long, 数量: 100.0, 入场价: 50.08
2025-06-26 18:42:34,230 - root - INFO - fetch_positions:4078 - 活跃持仓: ETH/USDT, 类型: spot, 方向: long, 数量: 1.0, 入场价: 2456.23
2025-06-26 18:42:34,230 - root - INFO - fetch_positions:4083 - 获取持仓: 3个活跃持仓 (合约: 0, 现货: 3)
2025-06-26 18:42:34,231 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:42:34,231 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:42:34,232 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:42:34,232 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:42:34,233 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:42:36,100 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:42:36,100 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:42:36,101 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:42:36,103 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:42:40,607 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:42:40,608 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:42:40,609 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:42:40,610 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:42:40,611 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:42:42,278 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:42:42,279 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:42:42,280 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:42:42,282 - root - WARNING - update_main_trading_table:2440 - 备用方法获取spot_count价格失败: okx does not have market symbol spot_count
2025-06-26 18:42:42,283 - root - WARNING - update_main_trading_table:2440 - 备用方法获取positions价格失败: okx does not have market symbol positions
2025-06-26 18:42:42,283 - root - WARNING - update_main_trading_table:2440 - 备用方法获取contract_count价格失败: okx does not have market symbol contract_count
2025-06-26 18:42:42,284 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:42:47,031 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:42:47,032 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:42:47,032 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:42:47,033 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:42:47,033 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:42:48,685 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:42:48,686 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:42:48,687 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:42:48,689 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:42:53,356 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:42:53,357 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:42:53,357 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:42:53,358 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:42:53,358 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:42:55,049 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:42:55,049 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:42:55,050 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:42:55,053 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:42:59,660 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:42:59,661 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:42:59,662 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:42:59,663 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:42:59,664 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:43:01,331 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:43:01,332 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:43:01,333 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:43:01,335 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:43:05,964 - root - INFO - fetch_positions:4078 - 活跃持仓: BTC/USDT, 类型: spot, 方向: long, 数量: 1.0, 入场价: 107386.1
2025-06-26 18:43:05,965 - root - INFO - fetch_positions:4078 - 活跃持仓: OKB/USDT, 类型: spot, 方向: long, 数量: 100.0, 入场价: 50.12
2025-06-26 18:43:05,965 - root - INFO - fetch_positions:4078 - 活跃持仓: ETH/USDT, 类型: spot, 方向: long, 数量: 1.0, 入场价: 2456.93
2025-06-26 18:43:05,966 - root - INFO - fetch_positions:4083 - 获取持仓: 3个活跃持仓 (合约: 0, 现货: 3)
2025-06-26 18:43:05,967 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:43:05,967 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:43:05,968 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:43:05,968 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:43:05,969 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:43:07,633 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:43:07,634 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:43:08,051 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:43:08,056 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:43:12,649 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:43:12,650 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:43:12,651 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:43:12,652 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:43:12,653 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:43:14,325 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:43:14,326 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:43:14,327 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:43:14,332 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:43:18,936 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:43:18,937 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:43:18,937 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:43:18,938 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:43:18,938 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:43:20,613 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:43:20,614 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:43:20,616 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:43:20,620 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:43:25,510 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:43:25,510 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:43:25,511 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:43:25,512 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:43:25,512 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:43:27,168 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:43:27,169 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:43:27,170 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:43:27,174 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:43:31,838 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:43:31,839 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:43:31,839 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:43:31,840 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:43:31,841 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:43:33,493 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:43:33,494 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:43:33,494 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:43:33,497 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:43:38,121 - root - INFO - fetch_positions:4078 - 活跃持仓: BTC/USDT, 类型: spot, 方向: long, 数量: 1.0, 入场价: 107378.9
2025-06-26 18:43:38,122 - root - INFO - fetch_positions:4078 - 活跃持仓: OKB/USDT, 类型: spot, 方向: long, 数量: 100.0, 入场价: 50.13
2025-06-26 18:43:38,123 - root - INFO - fetch_positions:4078 - 活跃持仓: ETH/USDT, 类型: spot, 方向: long, 数量: 1.0, 入场价: 2457.5
2025-06-26 18:43:38,124 - root - INFO - fetch_positions:4083 - 获取持仓: 3个活跃持仓 (合约: 0, 现货: 3)
2025-06-26 18:43:38,125 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:43:38,126 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:43:38,127 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:43:38,128 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:43:38,128 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:43:39,792 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:43:39,793 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:43:39,794 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:43:39,796 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:43:44,486 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:43:44,487 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:43:44,487 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:43:44,488 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:43:44,488 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:43:46,451 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:43:46,451 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:43:46,452 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:43:46,454 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:43:51,135 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:43:51,136 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:43:51,136 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:43:51,137 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:43:51,138 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:43:52,814 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:43:52,815 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:43:52,816 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:43:52,818 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:43:57,807 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:43:57,808 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:43:57,809 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:43:57,809 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:43:57,810 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:43:59,479 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:43:59,479 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:43:59,480 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:43:59,483 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:44:04,186 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:44:04,187 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:44:04,188 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:44:04,188 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:44:04,189 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:44:05,961 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:44:05,962 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:44:05,963 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:44:05,965 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:44:10,625 - root - INFO - fetch_positions:4078 - 活跃持仓: BTC/USDT, 类型: spot, 方向: long, 数量: 1.0, 入场价: 107370.1
2025-06-26 18:44:10,626 - root - INFO - fetch_positions:4078 - 活跃持仓: OKB/USDT, 类型: spot, 方向: long, 数量: 100.0, 入场价: 50.13
2025-06-26 18:44:10,627 - root - INFO - fetch_positions:4078 - 活跃持仓: ETH/USDT, 类型: spot, 方向: long, 数量: 1.0, 入场价: 2456.86
2025-06-26 18:44:10,628 - root - INFO - fetch_positions:4083 - 获取持仓: 3个活跃持仓 (合约: 0, 现货: 3)
2025-06-26 18:44:10,629 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:44:10,629 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:44:10,630 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:44:10,631 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:44:10,632 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:44:11,879 - root - INFO - _generate_performance_report:3083 - 
=== 实时监控性能报告 ===
总持仓数量: 0
风险分布: 高风险(0) | 中风险(0) | 低风险(0)
投资组合风险: 0.000
平均盈亏: 0.00%
活跃持仓数: 0
智能优化状态:
  - 智能平仓优化: ✅ 启用
  - 智能开仓优化: ✅ 启用
  - 动态止损优化: ✅ 启用
========================

2025-06-26 18:44:12,300 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:44:12,301 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:44:12,716 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:44:12,718 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:44:13,716 - root - INFO - update_main_trading_table:2817 - 表格强制更新(第20次) - 性能优化版本
2025-06-26 18:44:17,205 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:44:17,205 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:44:17,206 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:44:17,206 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:44:17,207 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:44:18,444 - strategy_core - ERROR - wrapper:513 - 策略执行失败: select_product.选品策略一, 错误: select_symbols_by_atr_adx() got an unexpected keyword argument 'min_adx'
2025-06-26 18:44:18,445 - root - ERROR - execute_select_product_strategy:1119 - 执行ATR/ADX策略失败: An asyncio.Future, a coroutine or an awaitable is required
2025-06-26 18:44:18,447 - root - INFO - trade_loop:1797 - 智能选品策略 选品策略一 选出的可开仓候选: ['BTC/USDT:USDT', 'ETH/USDT:USDT', 'BNB/USDT:USDT']
2025-06-26 18:44:18,852 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:44:18,853 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:44:18,853 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:44:18,855 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:44:23,749 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:44:23,762 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.013s)
2025-06-26 18:44:24,808 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:44:25,285 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:44:25,310 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:44:26,932 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:44:26,933 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:44:26,934 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:44:26,935 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:44:31,608 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:44:31,610 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:44:31,611 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:44:31,612 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:44:31,613 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:44:33,289 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:44:33,290 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:44:33,869 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:44:33,870 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:44:38,108 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:44:38,109 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:44:38,110 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:44:38,110 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:44:38,111 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:44:39,966 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:44:39,966 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:44:39,967 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:44:39,969 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:44:44,604 - root - INFO - fetch_positions:4078 - 活跃持仓: BTC/USDT, 类型: spot, 方向: long, 数量: 1.0, 入场价: 107366.1
2025-06-26 18:44:44,605 - root - INFO - fetch_positions:4078 - 活跃持仓: OKB/USDT, 类型: spot, 方向: long, 数量: 100.0, 入场价: 50.13
2025-06-26 18:44:44,606 - root - INFO - fetch_positions:4078 - 活跃持仓: ETH/USDT, 类型: spot, 方向: long, 数量: 1.0, 入场价: 2456.91
2025-06-26 18:44:44,607 - root - INFO - fetch_positions:4083 - 获取持仓: 3个活跃持仓 (合约: 0, 现货: 3)
2025-06-26 18:44:44,608 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:44:44,609 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:44:44,610 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:44:44,611 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:44:44,612 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:44:46,439 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:44:46,440 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:44:46,442 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:44:46,446 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:44:51,200 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:44:51,201 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:44:51,202 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:44:51,203 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:44:51,204 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:44:52,894 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:44:52,895 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:44:52,895 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:44:52,897 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:44:58,204 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:44:58,206 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:44:58,207 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:44:58,207 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:44:58,208 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:44:59,931 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:44:59,932 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:44:59,933 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:44:59,937 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:45:04,553 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:45:04,554 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:45:04,555 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:45:04,556 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:45:04,557 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:45:06,283 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:45:06,284 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:45:06,286 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:45:06,290 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:45:11,035 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:45:11,036 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:45:11,036 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:45:11,037 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:45:11,037 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:45:12,851 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:45:12,852 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:45:13,321 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:45:13,325 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:45:18,324 - root - INFO - fetch_positions:4078 - 活跃持仓: BTC/USDT, 类型: spot, 方向: long, 数量: 1.0, 入场价: 107414.9
2025-06-26 18:45:18,325 - root - INFO - fetch_positions:4078 - 活跃持仓: OKB/USDT, 类型: spot, 方向: long, 数量: 100.0, 入场价: 50.08
2025-06-26 18:45:18,325 - root - INFO - fetch_positions:4078 - 活跃持仓: ETH/USDT, 类型: spot, 方向: long, 数量: 1.0, 入场价: 2457.86
2025-06-26 18:45:18,326 - root - INFO - fetch_positions:4083 - 获取持仓: 3个活跃持仓 (合约: 0, 现货: 3)
2025-06-26 18:45:18,327 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:45:18,327 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:45:18,328 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:45:18,329 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:45:18,329 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:45:20,006 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:45:20,007 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:45:20,008 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:45:20,013 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:45:24,700 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:45:24,701 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:45:24,702 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:45:24,703 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:45:24,704 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:45:26,726 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:45:26,728 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:45:26,729 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:45:26,733 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:45:31,370 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:45:31,371 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:45:31,372 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:45:31,373 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:45:31,374 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:45:33,295 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:45:33,297 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:45:33,298 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:45:33,302 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:45:37,676 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:45:37,678 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:45:37,679 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:45:37,680 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:45:37,681 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:45:39,900 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:45:39,900 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:45:39,901 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:45:39,903 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:45:44,511 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:45:44,512 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:45:44,513 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:45:44,514 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:45:44,515 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:45:46,230 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:45:46,231 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:45:46,232 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:45:46,236 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:45:51,056 - root - INFO - fetch_positions:4078 - 活跃持仓: BTC/USDT, 类型: spot, 方向: long, 数量: 1.0, 入场价: 107414.3
2025-06-26 18:45:51,056 - root - INFO - fetch_positions:4078 - 活跃持仓: OKB/USDT, 类型: spot, 方向: long, 数量: 100.0, 入场价: 50.12
2025-06-26 18:45:51,057 - root - INFO - fetch_positions:4078 - 活跃持仓: ETH/USDT, 类型: spot, 方向: long, 数量: 1.0, 入场价: 2457.92
2025-06-26 18:45:51,057 - root - INFO - fetch_positions:4083 - 获取持仓: 3个活跃持仓 (合约: 0, 现货: 3)
2025-06-26 18:45:51,058 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:45:51,058 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.000s)
2025-06-26 18:45:51,058 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:45:51,059 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:45:51,059 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:45:53,029 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:45:53,030 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:45:53,031 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:45:53,032 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:45:57,766 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:45:57,767 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:45:57,768 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:45:57,769 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:45:57,770 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:45:59,473 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:45:59,474 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:45:59,475 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:45:59,477 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:46:04,153 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:46:04,154 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:46:04,154 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:46:04,155 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:46:04,156 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:46:05,817 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:46:05,859 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:46:05,868 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:46:05,908 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:46:10,425 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:46:10,426 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:46:10,427 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:46:10,428 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:46:10,428 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:46:12,109 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:46:12,111 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:46:12,112 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:46:12,116 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:46:16,758 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:46:16,759 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:46:16,760 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:46:16,761 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:46:16,762 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:46:18,417 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:46:18,419 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:46:18,835 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:46:18,839 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:46:23,094 - root - INFO - fetch_positions:4078 - 活跃持仓: BTC/USDT, 类型: spot, 方向: long, 数量: 1.0, 入场价: 107425.5
2025-06-26 18:46:23,095 - root - INFO - fetch_positions:4078 - 活跃持仓: OKB/USDT, 类型: spot, 方向: long, 数量: 100.0, 入场价: 50.12
2025-06-26 18:46:23,096 - root - INFO - fetch_positions:4078 - 活跃持仓: ETH/USDT, 类型: spot, 方向: long, 数量: 1.0, 入场价: 2458.25
2025-06-26 18:46:23,097 - root - INFO - fetch_positions:4083 - 获取持仓: 3个活跃持仓 (合约: 0, 现货: 3)
2025-06-26 18:46:23,098 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:46:23,099 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:46:23,100 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:46:23,101 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:46:23,102 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:46:24,356 - root - INFO - _generate_performance_report:3083 - 
=== 实时监控性能报告 ===
总持仓数量: 0
风险分布: 高风险(0) | 中风险(0) | 低风险(0)
投资组合风险: 0.000
平均盈亏: 0.00%
活跃持仓数: 0
智能优化状态:
  - 智能平仓优化: ✅ 启用
  - 智能开仓优化: ✅ 启用
  - 动态止损优化: ✅ 启用
========================

2025-06-26 18:46:24,758 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:46:24,759 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:46:24,760 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:46:24,764 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:46:25,760 - root - INFO - update_main_trading_table:2817 - 表格强制更新(第40次) - 性能优化版本
2025-06-26 18:46:29,393 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:46:29,394 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:46:29,395 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:46:29,396 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:46:29,397 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:46:31,047 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:46:31,048 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:46:31,050 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:46:31,054 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:46:35,927 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:46:35,928 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:46:35,930 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:46:35,931 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:46:35,932 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:46:37,605 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:46:37,606 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:46:37,608 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:46:37,612 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:46:42,246 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:46:42,247 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:46:42,248 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:46:42,249 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:46:42,250 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:46:44,118 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:46:44,119 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:46:44,120 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:46:44,124 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:46:48,843 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:46:48,844 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:46:48,845 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:46:48,846 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:46:48,847 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:46:50,680 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:46:50,681 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:46:50,682 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:46:50,687 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:46:55,524 - root - INFO - fetch_positions:4078 - 活跃持仓: BTC/USDT, 类型: spot, 方向: long, 数量: 1.0, 入场价: 107369.5
2025-06-26 18:46:55,525 - root - INFO - fetch_positions:4078 - 活跃持仓: OKB/USDT, 类型: spot, 方向: long, 数量: 100.0, 入场价: 50.1
2025-06-26 18:46:55,526 - root - INFO - fetch_positions:4078 - 活跃持仓: ETH/USDT, 类型: spot, 方向: long, 数量: 1.0, 入场价: 2456.5
2025-06-26 18:46:55,528 - root - INFO - fetch_positions:4083 - 获取持仓: 3个活跃持仓 (合约: 0, 现货: 3)
2025-06-26 18:46:55,529 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:46:55,530 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:46:55,532 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:46:55,533 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:46:55,534 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:46:57,199 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:46:57,200 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:46:57,201 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:46:57,206 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:47:01,847 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:47:01,848 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:47:01,849 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:47:01,849 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:47:01,850 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:47:03,522 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:47:03,523 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:47:03,525 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:47:03,529 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:47:08,150 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:47:08,151 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:47:08,151 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:47:08,152 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:47:08,152 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:47:09,421 - strategy_core - ERROR - wrapper:513 - 策略执行失败: select_product.选品策略一, 错误: select_symbols_by_atr_adx() got an unexpected keyword argument 'min_adx'
2025-06-26 18:47:09,427 - root - ERROR - execute_select_product_strategy:1119 - 执行ATR/ADX策略失败: An asyncio.Future, a coroutine or an awaitable is required
2025-06-26 18:47:09,430 - root - INFO - trade_loop:1797 - 智能选品策略 选品策略一 选出的可开仓候选: ['BTC/USDT:USDT', 'ETH/USDT:USDT', 'BNB/USDT:USDT']
2025-06-26 18:47:09,850 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:47:09,851 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:47:09,852 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:47:09,854 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:47:14,502 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:47:14,503 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:47:14,504 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:47:14,504 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:47:14,505 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:47:16,198 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:47:16,199 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:47:16,200 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:47:16,202 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:47:20,857 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:47:20,858 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:47:20,859 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:47:20,860 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:47:20,860 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:47:22,534 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:47:22,537 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:47:22,931 - root - INFO - update_main_trading_table:2309 - 账户余额更新: 5000.00 USDT (缓存60s)
2025-06-26 18:47:22,932 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:47:22,934 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:47:27,150 - root - INFO - fetch_positions:4078 - 活跃持仓: BTC/USDT, 类型: spot, 方向: long, 数量: 1.0, 入场价: 107386.0
2025-06-26 18:47:27,151 - root - INFO - fetch_positions:4078 - 活跃持仓: OKB/USDT, 类型: spot, 方向: long, 数量: 100.0, 入场价: 50.07
2025-06-26 18:47:27,152 - root - INFO - fetch_positions:4078 - 活跃持仓: ETH/USDT, 类型: spot, 方向: long, 数量: 1.0, 入场价: 2457.21
2025-06-26 18:47:27,153 - root - INFO - fetch_positions:4083 - 获取持仓: 3个活跃持仓 (合约: 0, 现货: 3)
2025-06-26 18:47:27,154 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:47:27,155 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:47:27,156 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:47:27,157 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:47:27,158 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:47:28,398 - root - INFO - trade_loop:2144 - 内存使用: 205.5MB, 循环计数: 50
2025-06-26 18:47:28,819 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:47:28,820 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:47:28,821 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:47:28,823 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:47:33,465 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:47:33,465 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:47:33,466 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:47:33,466 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:47:33,467 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:47:35,398 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:47:35,399 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:47:35,401 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:47:35,405 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:47:40,047 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:47:40,047 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:47:40,048 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:47:40,049 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:47:40,049 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:47:41,717 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:47:41,718 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:47:41,719 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:47:41,722 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:47:46,549 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:47:46,550 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:47:46,551 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:47:46,552 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:47:46,553 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:47:48,223 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:47:48,224 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:47:48,226 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:47:48,229 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:47:52,838 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:47:52,839 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:47:52,840 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:47:52,840 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:47:52,841 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:47:54,519 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:47:54,519 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:47:54,520 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:47:54,523 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:47:59,140 - root - INFO - fetch_positions:4078 - 活跃持仓: BTC/USDT, 类型: spot, 方向: long, 数量: 1.0, 入场价: 107396.0
2025-06-26 18:47:59,141 - root - INFO - fetch_positions:4078 - 活跃持仓: OKB/USDT, 类型: spot, 方向: long, 数量: 100.0, 入场价: 50.07
2025-06-26 18:47:59,141 - root - INFO - fetch_positions:4078 - 活跃持仓: ETH/USDT, 类型: spot, 方向: long, 数量: 1.0, 入场价: 2457.81
2025-06-26 18:47:59,142 - root - INFO - fetch_positions:4083 - 获取持仓: 3个活跃持仓 (合约: 0, 现货: 3)
2025-06-26 18:47:59,142 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:47:59,143 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:47:59,143 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:47:59,144 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:47:59,144 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:48:01,036 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:48:01,044 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:48:01,045 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:48:01,080 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:48:05,696 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:48:05,697 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:48:05,697 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:48:05,698 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:48:05,698 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:48:07,698 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:48:07,698 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:48:07,700 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:48:07,702 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:48:12,052 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:48:12,053 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:48:12,053 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:48:12,054 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:48:12,055 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:48:13,766 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:48:13,767 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:48:13,768 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:48:13,771 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:48:18,424 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:48:18,424 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:48:18,425 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:48:18,425 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:48:18,426 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:48:20,102 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:48:20,103 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:48:20,103 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:48:20,106 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:48:25,105 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:48:25,105 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:48:25,106 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:48:25,106 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:48:25,107 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:48:27,028 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:48:27,029 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:48:27,478 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:48:27,480 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:48:31,746 - root - INFO - fetch_positions:4078 - 活跃持仓: BTC/USDT, 类型: spot, 方向: long, 数量: 1.0, 入场价: 107388.0
2025-06-26 18:48:31,775 - root - INFO - fetch_positions:4078 - 活跃持仓: OKB/USDT, 类型: spot, 方向: long, 数量: 100.0, 入场价: 50.11
2025-06-26 18:48:31,776 - root - INFO - fetch_positions:4078 - 活跃持仓: ETH/USDT, 类型: spot, 方向: long, 数量: 1.0, 入场价: 2457.6
2025-06-26 18:48:31,778 - root - INFO - fetch_positions:4083 - 获取持仓: 3个活跃持仓 (合约: 0, 现货: 3)
2025-06-26 18:48:31,779 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:48:31,781 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.002s)
2025-06-26 18:48:31,782 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:48:31,809 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:48:31,817 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:48:33,595 - root - INFO - _generate_performance_report:3083 - 
=== 实时监控性能报告 ===
总持仓数量: 0
风险分布: 高风险(0) | 中风险(0) | 低风险(0)
投资组合风险: 0.000
平均盈亏: 0.00%
活跃持仓数: 0
智能优化状态:
  - 智能平仓优化: ✅ 启用
  - 智能开仓优化: ✅ 启用
  - 动态止损优化: ✅ 启用
========================

2025-06-26 18:48:34,014 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:48:34,015 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:48:34,015 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:48:34,018 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:48:35,013 - root - INFO - update_main_trading_table:2817 - 表格强制更新(第60次) - 性能优化版本
2025-06-26 18:48:38,659 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:48:38,660 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:48:38,660 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:48:38,661 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:48:38,661 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:48:40,319 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:48:40,321 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:48:40,326 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:48:40,344 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:48:44,979 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:48:44,980 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:48:44,981 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:48:44,981 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:48:44,982 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:48:46,671 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:48:46,672 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:48:46,673 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:48:46,675 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:48:51,524 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:48:51,525 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:48:51,525 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:48:51,526 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:48:51,526 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:48:53,232 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:48:53,232 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:48:53,233 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:48:53,235 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:48:57,904 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:48:57,905 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:48:57,905 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:48:57,906 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:48:57,906 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:48:59,572 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:48:59,573 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:48:59,573 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:48:59,576 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:49:04,175 - root - INFO - fetch_positions:4078 - 活跃持仓: BTC/USDT, 类型: spot, 方向: long, 数量: 1.0, 入场价: 107388.1
2025-06-26 18:49:04,176 - root - INFO - fetch_positions:4078 - 活跃持仓: OKB/USDT, 类型: spot, 方向: long, 数量: 100.0, 入场价: 50.15
2025-06-26 18:49:04,176 - root - INFO - fetch_positions:4078 - 活跃持仓: ETH/USDT, 类型: spot, 方向: long, 数量: 1.0, 入场价: 2457.42
2025-06-26 18:49:04,177 - root - INFO - fetch_positions:4083 - 获取持仓: 3个活跃持仓 (合约: 0, 现货: 3)
2025-06-26 18:49:04,178 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:49:04,178 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:49:04,179 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:49:04,179 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:49:04,180 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:49:05,901 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:49:05,902 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:49:05,903 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:49:05,905 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:49:10,528 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:49:10,529 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:49:10,530 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:49:10,531 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:49:10,531 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:49:12,211 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:49:12,212 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:49:12,214 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:49:12,218 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:49:16,860 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:49:16,861 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:49:16,861 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:49:16,862 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:49:16,863 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:49:18,523 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:49:18,524 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:49:18,526 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:49:18,530 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:49:23,164 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:49:23,166 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:49:23,167 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:49:23,168 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:49:23,169 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:49:25,270 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:49:25,271 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:49:25,272 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:49:25,276 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:49:29,732 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:49:29,733 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:49:29,734 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:49:29,735 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:49:29,736 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:49:31,410 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:49:31,411 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:49:31,829 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:49:31,833 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:49:36,104 - root - INFO - fetch_positions:4078 - 活跃持仓: BTC/USDT, 类型: spot, 方向: long, 数量: 1.0, 入场价: 107388.1
2025-06-26 18:49:36,104 - root - INFO - fetch_positions:4078 - 活跃持仓: OKB/USDT, 类型: spot, 方向: long, 数量: 100.0, 入场价: 50.12
2025-06-26 18:49:36,105 - root - INFO - fetch_positions:4078 - 活跃持仓: ETH/USDT, 类型: spot, 方向: long, 数量: 1.0, 入场价: 2457.72
2025-06-26 18:49:36,105 - root - INFO - fetch_positions:4083 - 获取持仓: 3个活跃持仓 (合约: 0, 现货: 3)
2025-06-26 18:49:36,106 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:49:36,106 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:49:36,107 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:49:36,107 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:49:36,108 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:49:37,763 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:49:37,765 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:49:37,766 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:49:37,770 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:49:42,679 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:49:42,681 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:49:42,682 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:49:42,683 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:49:42,684 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:49:44,604 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:49:44,605 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:49:44,606 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:49:44,610 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:49:49,007 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:49:49,008 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:49:49,009 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:49:49,010 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:49:49,011 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:49:50,671 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:49:50,672 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:49:50,672 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:49:50,674 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:49:55,329 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:49:55,330 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:49:55,331 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:49:55,332 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:49:55,333 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:49:56,584 - strategy_core - ERROR - wrapper:513 - 策略执行失败: select_product.选品策略一, 错误: select_symbols_by_atr_adx() got an unexpected keyword argument 'min_adx'
2025-06-26 18:49:56,586 - root - ERROR - execute_select_product_strategy:1119 - 执行ATR/ADX策略失败: An asyncio.Future, a coroutine or an awaitable is required
2025-06-26 18:49:56,588 - root - INFO - trade_loop:1797 - 智能选品策略 选品策略一 选出的可开仓候选: ['BTC/USDT:USDT', 'ETH/USDT:USDT', 'BNB/USDT:USDT']
2025-06-26 18:49:56,999 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:49:57,000 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:49:57,000 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:49:57,002 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:50:01,800 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:50:01,801 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:50:01,802 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:50:01,803 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:50:01,804 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:50:04,108 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:50:04,109 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:50:04,110 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:50:04,114 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:50:08,766 - root - INFO - fetch_positions:4078 - 活跃持仓: BTC/USDT, 类型: spot, 方向: long, 数量: 1.0, 入场价: 107376.7
2025-06-26 18:50:08,767 - root - INFO - fetch_positions:4078 - 活跃持仓: OKB/USDT, 类型: spot, 方向: long, 数量: 100.0, 入场价: 50.12
2025-06-26 18:50:08,768 - root - INFO - fetch_positions:4078 - 活跃持仓: ETH/USDT, 类型: spot, 方向: long, 数量: 1.0, 入场价: 2456.8
2025-06-26 18:50:08,768 - root - INFO - fetch_positions:4083 - 获取持仓: 3个活跃持仓 (合约: 0, 现货: 3)
2025-06-26 18:50:08,769 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:50:08,769 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:50:08,770 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:50:08,771 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:50:08,771 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:50:10,423 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:50:10,424 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:50:10,425 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:50:10,429 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:50:15,087 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:50:15,089 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:50:15,090 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:50:15,091 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:50:15,092 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:50:16,760 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:50:16,761 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:50:16,761 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:50:16,763 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:50:21,510 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:50:21,511 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:50:21,512 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:50:21,513 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:50:21,514 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:50:23,184 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:50:23,185 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:50:23,187 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:50:23,191 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:50:28,083 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:50:28,084 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:50:28,085 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:50:28,086 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:50:28,087 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:50:29,757 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:50:29,758 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:50:29,760 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:50:29,764 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:50:34,433 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:50:34,434 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:50:34,435 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:50:34,436 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:50:34,437 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:50:36,110 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:50:36,111 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:50:36,532 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:50:36,536 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:50:41,397 - root - INFO - fetch_positions:4078 - 活跃持仓: BTC/USDT, 类型: spot, 方向: long, 数量: 1.0, 入场价: 107386.1
2025-06-26 18:50:41,398 - root - INFO - fetch_positions:4078 - 活跃持仓: OKB/USDT, 类型: spot, 方向: long, 数量: 100.0, 入场价: 50.09
2025-06-26 18:50:41,399 - root - INFO - fetch_positions:4078 - 活跃持仓: ETH/USDT, 类型: spot, 方向: long, 数量: 1.0, 入场价: 2456.19
2025-06-26 18:50:41,400 - root - INFO - fetch_positions:4083 - 获取持仓: 3个活跃持仓 (合约: 0, 现货: 3)
2025-06-26 18:50:41,402 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:50:41,403 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:50:41,404 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:50:41,405 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:50:41,406 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:50:42,652 - root - INFO - _generate_performance_report:3083 - 
=== 实时监控性能报告 ===
总持仓数量: 0
风险分布: 高风险(0) | 中风险(0) | 低风险(0)
投资组合风险: 0.000
平均盈亏: 0.00%
活跃持仓数: 0
智能优化状态:
  - 智能平仓优化: ✅ 启用
  - 智能开仓优化: ✅ 启用
  - 动态止损优化: ✅ 启用
========================

2025-06-26 18:50:43,070 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:50:43,071 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:50:43,072 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:50:43,076 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:50:44,077 - root - INFO - update_main_trading_table:2817 - 表格强制更新(第80次) - 性能优化版本
2025-06-26 18:50:48,012 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:50:48,013 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:50:48,013 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:50:48,014 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:50:48,014 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:50:49,686 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:50:49,686 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:50:49,687 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:50:49,689 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:50:54,315 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:50:54,316 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:50:54,317 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:50:54,318 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:50:54,319 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:50:56,255 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:50:56,256 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:50:56,257 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:50:56,262 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:51:00,729 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:51:00,730 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:51:00,731 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:51:00,732 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:51:00,733 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:51:02,425 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:51:02,427 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:51:02,428 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:51:02,432 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:51:07,061 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:51:07,063 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:51:07,064 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:51:07,065 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:51:07,066 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:51:08,793 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:51:08,793 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:51:08,794 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:51:08,796 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:51:13,618 - root - INFO - fetch_positions:4078 - 活跃持仓: BTC/USDT, 类型: spot, 方向: long, 数量: 1.0, 入场价: 107430.0
2025-06-26 18:51:13,619 - root - INFO - fetch_positions:4078 - 活跃持仓: OKB/USDT, 类型: spot, 方向: long, 数量: 100.0, 入场价: 50.09
2025-06-26 18:51:13,620 - root - INFO - fetch_positions:4078 - 活跃持仓: ETH/USDT, 类型: spot, 方向: long, 数量: 1.0, 入场价: 2457.55
2025-06-26 18:51:13,621 - root - INFO - fetch_positions:4083 - 获取持仓: 3个活跃持仓 (合约: 0, 现货: 3)
2025-06-26 18:51:13,622 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:51:13,623 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:51:13,624 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:51:13,626 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:51:13,627 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:51:15,546 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:51:15,547 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:51:15,548 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:51:15,552 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:51:20,334 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:51:20,335 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:51:20,336 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:51:20,337 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:51:20,338 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:51:22,006 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:51:22,007 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:51:22,009 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:51:22,013 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:51:26,656 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:51:26,657 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:51:26,658 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:51:26,659 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:51:26,660 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:51:28,338 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:51:28,339 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:51:28,340 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:51:28,344 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:51:33,166 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:51:33,167 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:51:33,168 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:51:33,169 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:51:33,170 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:51:34,809 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:51:34,809 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:51:34,810 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:51:34,812 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:51:39,447 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:51:39,449 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:51:39,450 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:51:39,451 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:51:39,452 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:51:41,162 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:51:41,163 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:51:41,579 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:51:41,583 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:51:45,800 - root - INFO - fetch_positions:4078 - 活跃持仓: BTC/USDT, 类型: spot, 方向: long, 数量: 1.0, 入场价: 107444.0
2025-06-26 18:51:45,801 - root - INFO - fetch_positions:4078 - 活跃持仓: OKB/USDT, 类型: spot, 方向: long, 数量: 100.0, 入场价: 50.09
2025-06-26 18:51:45,801 - root - INFO - fetch_positions:4078 - 活跃持仓: ETH/USDT, 类型: spot, 方向: long, 数量: 1.0, 入场价: 2458.77
2025-06-26 18:51:45,802 - root - INFO - fetch_positions:4083 - 获取持仓: 3个活跃持仓 (合约: 0, 现货: 3)
2025-06-26 18:51:45,802 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:51:45,802 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.000s)
2025-06-26 18:51:45,803 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:51:45,803 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:51:45,803 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:51:47,660 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:51:47,661 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:51:47,663 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:51:47,667 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:51:52,303 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:51:52,304 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:51:52,304 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:51:52,305 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:51:52,305 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:51:54,015 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:51:54,016 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:51:54,018 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:51:54,022 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:51:58,799 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:51:58,800 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:51:58,801 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:51:58,802 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:51:58,803 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:52:00,474 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:52:00,475 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:52:00,476 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:52:00,480 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:52:05,402 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:52:05,403 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:52:05,404 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:52:05,405 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:52:05,406 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:52:07,081 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:52:07,082 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:52:07,083 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:52:07,087 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:52:11,721 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:52:11,722 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:52:11,723 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:52:11,724 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:52:11,725 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:52:13,396 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:52:13,397 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:52:13,399 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:52:13,403 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:52:18,542 - root - INFO - fetch_positions:4078 - 活跃持仓: BTC/USDT, 类型: spot, 方向: long, 数量: 1.0, 入场价: 107457.2
2025-06-26 18:52:18,543 - root - INFO - fetch_positions:4078 - 活跃持仓: OKB/USDT, 类型: spot, 方向: long, 数量: 100.0, 入场价: 50.09
2025-06-26 18:52:18,544 - root - INFO - fetch_positions:4078 - 活跃持仓: ETH/USDT, 类型: spot, 方向: long, 数量: 1.0, 入场价: 2458.7
2025-06-26 18:52:18,545 - root - INFO - fetch_positions:4083 - 获取持仓: 3个活跃持仓 (合约: 0, 现货: 3)
2025-06-26 18:52:18,546 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:52:18,547 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:52:18,548 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:52:18,549 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:52:18,550 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:52:20,498 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:52:20,499 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:52:20,500 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:52:20,504 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:52:25,026 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:52:25,028 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:52:25,029 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:52:25,030 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:52:25,031 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:52:26,716 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:52:26,717 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:52:26,719 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:52:26,723 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:52:31,626 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:52:31,627 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:52:31,627 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:52:31,628 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:52:31,628 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:52:33,431 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:52:33,431 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:52:33,432 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:52:33,434 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:52:38,103 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:52:38,104 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:52:38,105 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:52:38,106 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:52:38,107 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:52:39,752 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:52:39,753 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:52:39,754 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:52:39,758 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:52:44,397 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:52:44,399 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:52:44,400 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:52:44,401 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:52:44,402 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:52:45,640 - strategy_core - ERROR - wrapper:513 - 策略执行失败: select_product.选品策略一, 错误: select_symbols_by_atr_adx() got an unexpected keyword argument 'min_adx'
2025-06-26 18:52:45,642 - root - ERROR - execute_select_product_strategy:1119 - 执行ATR/ADX策略失败: An asyncio.Future, a coroutine or an awaitable is required
2025-06-26 18:52:45,644 - root - INFO - trade_loop:1797 - 智能选品策略 选品策略一 选出的可开仓候选: ['BTC/USDT:USDT', 'ETH/USDT:USDT', 'BNB/USDT:USDT']
2025-06-26 18:52:46,053 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:52:46,054 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:52:46,720 - root - INFO - update_main_trading_table:2309 - 账户余额更新: 5000.00 USDT (缓存60s)
2025-06-26 18:52:46,722 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:52:46,726 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:52:50,935 - root - INFO - fetch_positions:4078 - 活跃持仓: BTC/USDT, 类型: spot, 方向: long, 数量: 1.0, 入场价: 107421.9
2025-06-26 18:52:50,936 - root - INFO - fetch_positions:4078 - 活跃持仓: OKB/USDT, 类型: spot, 方向: long, 数量: 100.0, 入场价: 50.07
2025-06-26 18:52:50,937 - root - INFO - fetch_positions:4078 - 活跃持仓: ETH/USDT, 类型: spot, 方向: long, 数量: 1.0, 入场价: 2457.89
2025-06-26 18:52:50,938 - root - INFO - fetch_positions:4083 - 获取持仓: 3个活跃持仓 (合约: 0, 现货: 3)
2025-06-26 18:52:50,939 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:52:50,940 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:52:50,941 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:52:50,942 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:52:50,943 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:52:52,190 - root - INFO - _generate_performance_report:3083 - 
=== 实时监控性能报告 ===
总持仓数量: 0
风险分布: 高风险(0) | 中风险(0) | 低风险(0)
投资组合风险: 0.000
平均盈亏: 0.00%
活跃持仓数: 0
智能优化状态:
  - 智能平仓优化: ✅ 启用
  - 智能开仓优化: ✅ 启用
  - 动态止损优化: ✅ 启用
========================

2025-06-26 18:52:52,195 - root - INFO - trade_loop:2144 - 内存使用: 205.6MB, 循环计数: 100
2025-06-26 18:52:52,592 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:52:52,593 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:52:52,594 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:52:52,599 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:52:53,653 - root - INFO - update_main_trading_table:2817 - 表格强制更新(第100次) - 性能优化版本
2025-06-26 18:52:53,654 - root - INFO - update_main_trading_table:2839 - UI性能统计(100次): 平均更新时间1.225s, 缓存命中率0%
2025-06-26 18:52:57,460 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:52:57,461 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:52:57,462 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:52:57,463 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:52:57,464 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:52:59,335 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:52:59,336 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:52:59,337 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:52:59,341 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:53:04,496 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:53:04,497 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:53:04,498 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:53:04,499 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:53:04,500 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:53:06,204 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:53:06,205 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:53:06,207 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:53:06,210 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:53:10,849 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:53:10,850 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:53:10,851 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:53:10,852 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:53:10,853 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:53:12,524 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:53:12,525 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:53:12,526 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:53:12,530 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:53:17,272 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:53:17,273 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:53:17,274 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:53:17,275 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:53:17,276 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:53:18,946 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:53:18,946 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:53:18,947 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:53:18,948 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:53:23,576 - root - INFO - fetch_positions:4078 - 活跃持仓: BTC/USDT, 类型: spot, 方向: long, 数量: 1.0, 入场价: 107424.9
2025-06-26 18:53:23,577 - root - INFO - fetch_positions:4078 - 活跃持仓: OKB/USDT, 类型: spot, 方向: long, 数量: 100.0, 入场价: 50.08
2025-06-26 18:53:23,578 - root - INFO - fetch_positions:4078 - 活跃持仓: ETH/USDT, 类型: spot, 方向: long, 数量: 1.0, 入场价: 2457.56
2025-06-26 18:53:23,579 - root - INFO - fetch_positions:4083 - 获取持仓: 3个活跃持仓 (合约: 0, 现货: 3)
2025-06-26 18:53:23,580 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:53:23,581 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:53:23,582 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:53:23,583 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:53:23,584 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:53:25,250 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:53:25,251 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:53:25,251 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:53:25,253 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:53:29,882 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:53:29,883 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:53:29,884 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:53:29,885 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:53:29,886 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:53:31,585 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:53:31,586 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:53:31,587 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:53:31,588 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:53:36,251 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:53:36,252 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:53:36,252 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:53:36,253 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:53:36,253 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:53:37,946 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:53:37,947 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:53:37,949 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:53:37,953 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:53:42,583 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:53:42,584 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:53:42,585 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:53:42,586 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:53:42,587 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:53:44,236 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:53:44,237 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:53:44,238 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:53:44,240 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:53:49,084 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:53:49,085 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:53:49,086 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:53:49,087 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:53:49,088 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:53:50,777 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:53:50,778 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:53:51,442 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:53:51,444 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:53:55,795 - root - INFO - fetch_positions:4078 - 活跃持仓: BTC/USDT, 类型: spot, 方向: long, 数量: 1.0, 入场价: 107348.4
2025-06-26 18:53:55,796 - root - INFO - fetch_positions:4078 - 活跃持仓: OKB/USDT, 类型: spot, 方向: long, 数量: 100.0, 入场价: 50.08
2025-06-26 18:53:55,796 - root - INFO - fetch_positions:4078 - 活跃持仓: ETH/USDT, 类型: spot, 方向: long, 数量: 1.0, 入场价: 2450.24
2025-06-26 18:53:55,797 - root - INFO - fetch_positions:4083 - 获取持仓: 3个活跃持仓 (合约: 0, 现货: 3)
2025-06-26 18:53:55,797 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:53:55,798 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.000s)
2025-06-26 18:53:55,798 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:53:55,798 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:53:55,799 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:53:57,470 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:53:57,471 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:53:57,472 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:53:57,477 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:54:02,136 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:54:02,136 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:54:02,137 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:54:02,138 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:54:02,138 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:54:03,861 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:54:03,862 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:54:03,863 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:54:03,867 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:54:09,062 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:54:09,063 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:54:09,063 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:54:09,063 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:54:09,064 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:54:11,175 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:54:11,176 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:54:11,177 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:54:11,181 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:54:15,424 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:54:15,424 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:54:15,425 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:54:15,425 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:54:15,426 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:54:17,105 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:54:17,106 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:54:17,107 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:54:17,111 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:54:21,772 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:54:21,773 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:54:21,774 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:54:21,775 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:54:21,776 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:54:23,458 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:54:23,460 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:54:23,461 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:54:23,465 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:54:28,098 - root - INFO - fetch_positions:4078 - 活跃持仓: BTC/USDT, 类型: spot, 方向: long, 数量: 1.0, 入场价: 107346.0
2025-06-26 18:54:28,099 - root - INFO - fetch_positions:4078 - 活跃持仓: OKB/USDT, 类型: spot, 方向: long, 数量: 100.0, 入场价: 50.08
2025-06-26 18:54:28,100 - root - INFO - fetch_positions:4078 - 活跃持仓: ETH/USDT, 类型: spot, 方向: long, 数量: 1.0, 入场价: 2450.39
2025-06-26 18:54:28,101 - root - INFO - fetch_positions:4083 - 获取持仓: 3个活跃持仓 (合约: 0, 现货: 3)
2025-06-26 18:54:28,102 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:54:28,103 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:54:28,104 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:54:28,105 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:54:28,106 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:54:29,830 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:54:29,831 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:54:29,832 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:54:29,836 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:54:34,884 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:54:34,884 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:54:34,885 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:54:34,885 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:54:34,885 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:54:36,592 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:54:36,593 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:54:36,594 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:54:36,598 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:54:41,238 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:54:41,239 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:54:41,240 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:54:41,240 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:54:41,241 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:54:42,908 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:54:42,909 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:54:42,910 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:54:42,914 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:54:47,588 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:54:47,589 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:54:47,589 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:54:47,590 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:54:47,590 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:54:49,315 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:54:49,316 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:54:49,318 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:54:49,322 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:54:53,925 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:54:53,926 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:54:53,926 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:54:53,927 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:54:53,927 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:54:55,832 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:54:55,833 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:54:56,303 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:54:56,307 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:55:00,726 - root - INFO - fetch_positions:4078 - 活跃持仓: BTC/USDT, 类型: spot, 方向: long, 数量: 1.0, 入场价: 107351.7
2025-06-26 18:55:00,728 - root - INFO - fetch_positions:4078 - 活跃持仓: OKB/USDT, 类型: spot, 方向: long, 数量: 100.0, 入场价: 50.08
2025-06-26 18:55:00,729 - root - INFO - fetch_positions:4078 - 活跃持仓: ETH/USDT, 类型: spot, 方向: long, 数量: 1.0, 入场价: 2449.89
2025-06-26 18:55:00,730 - root - INFO - fetch_positions:4083 - 获取持仓: 3个活跃持仓 (合约: 0, 现货: 3)
2025-06-26 18:55:00,731 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:55:00,732 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:55:00,733 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:55:00,734 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:55:00,735 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:55:01,962 - root - INFO - _generate_performance_report:3083 - 
=== 实时监控性能报告 ===
总持仓数量: 0
风险分布: 高风险(0) | 中风险(0) | 低风险(0)
投资组合风险: 0.000
平均盈亏: 0.00%
活跃持仓数: 0
智能优化状态:
  - 智能平仓优化: ✅ 启用
  - 智能开仓优化: ✅ 启用
  - 动态止损优化: ✅ 启用
========================

2025-06-26 18:55:02,380 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:55:02,381 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:55:02,382 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:55:02,384 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:55:03,386 - root - INFO - update_main_trading_table:2817 - 表格强制更新(第120次) - 性能优化版本
2025-06-26 18:55:07,022 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:55:07,024 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:55:07,025 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:55:07,026 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:55:07,027 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:55:08,785 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:55:08,786 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:55:08,787 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:55:08,791 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:55:13,427 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:55:13,428 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:55:13,429 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:55:13,430 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:55:13,431 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:55:15,107 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:55:15,108 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:55:15,108 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:55:15,110 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:55:19,731 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:55:19,732 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:55:19,733 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:55:19,734 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:55:19,735 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:55:21,395 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:55:21,396 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:55:21,398 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:55:21,402 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:55:26,008 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:55:26,008 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:55:26,009 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:55:26,009 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:55:26,010 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:55:27,690 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:55:27,691 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:55:27,692 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:55:27,696 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:55:32,337 - root - INFO - fetch_positions:4078 - 活跃持仓: BTC/USDT, 类型: spot, 方向: long, 数量: 1.0, 入场价: 107370.7
2025-06-26 18:55:32,338 - root - INFO - fetch_positions:4078 - 活跃持仓: OKB/USDT, 类型: spot, 方向: long, 数量: 100.0, 入场价: 50.08
2025-06-26 18:55:32,339 - root - INFO - fetch_positions:4078 - 活跃持仓: ETH/USDT, 类型: spot, 方向: long, 数量: 1.0, 入场价: 2451.69
2025-06-26 18:55:32,340 - root - INFO - fetch_positions:4083 - 获取持仓: 3个活跃持仓 (合约: 0, 现货: 3)
2025-06-26 18:55:32,341 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:55:32,342 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:55:32,343 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:55:32,344 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:55:32,345 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:55:33,587 - strategy_core - ERROR - wrapper:513 - 策略执行失败: select_product.选品策略一, 错误: select_symbols_by_atr_adx() got an unexpected keyword argument 'min_adx'
2025-06-26 18:55:33,589 - root - ERROR - execute_select_product_strategy:1119 - 执行ATR/ADX策略失败: An asyncio.Future, a coroutine or an awaitable is required
2025-06-26 18:55:33,591 - root - INFO - trade_loop:1797 - 智能选品策略 选品策略一 选出的可开仓候选: ['BTC/USDT:USDT', 'ETH/USDT:USDT', 'BNB/USDT:USDT']
2025-06-26 18:55:34,003 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:55:34,004 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:55:34,005 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:55:34,009 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:55:38,681 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:55:38,683 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:55:38,684 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:55:38,685 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:55:38,686 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:55:40,522 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:55:40,523 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:55:40,524 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:55:40,528 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:55:45,462 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:55:45,463 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:55:45,463 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:55:45,463 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:55:45,464 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:55:47,654 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:55:47,655 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:55:47,656 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:55:47,660 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:55:52,452 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:55:52,453 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:55:52,454 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:55:52,455 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:55:52,456 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:55:54,375 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:55:54,375 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:55:54,376 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:55:54,377 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:55:59,334 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:55:59,335 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:55:59,336 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:55:59,337 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:55:59,338 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:56:00,996 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:56:00,997 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:56:01,416 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:56:01,420 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:56:05,759 - root - INFO - fetch_positions:4078 - 活跃持仓: BTC/USDT, 类型: spot, 方向: long, 数量: 1.0, 入场价: 107398.0
2025-06-26 18:56:05,760 - root - INFO - fetch_positions:4078 - 活跃持仓: OKB/USDT, 类型: spot, 方向: long, 数量: 100.0, 入场价: 50.1
2025-06-26 18:56:05,760 - root - INFO - fetch_positions:4078 - 活跃持仓: ETH/USDT, 类型: spot, 方向: long, 数量: 1.0, 入场价: 2452.78
2025-06-26 18:56:05,760 - root - INFO - fetch_positions:4083 - 获取持仓: 3个活跃持仓 (合约: 0, 现货: 3)
2025-06-26 18:56:05,761 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:56:05,761 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.000s)
2025-06-26 18:56:05,762 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:56:05,762 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:56:05,763 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:56:07,411 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:56:07,412 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:56:07,413 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:56:07,417 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:56:12,367 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:56:12,368 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:56:12,369 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:56:12,370 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:56:12,371 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:56:14,379 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:56:14,380 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:56:14,381 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:56:14,385 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:56:18,955 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:56:18,956 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:56:18,957 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:56:18,958 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:56:18,959 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:56:20,879 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:56:20,880 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:56:20,881 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:56:20,885 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:56:25,240 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:56:25,241 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:56:25,242 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:56:25,243 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:56:25,244 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:56:26,944 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:56:26,945 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:56:26,946 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:56:26,950 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:56:31,591 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:56:31,592 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:56:31,593 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:56:31,594 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:56:31,595 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:56:33,315 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:56:33,316 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:56:33,317 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:56:33,321 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:56:38,198 - root - INFO - fetch_positions:4078 - 活跃持仓: BTC/USDT, 类型: spot, 方向: long, 数量: 1.0, 入场价: 107398.1
2025-06-26 18:56:38,199 - root - INFO - fetch_positions:4078 - 活跃持仓: OKB/USDT, 类型: spot, 方向: long, 数量: 100.0, 入场价: 50.07
2025-06-26 18:56:38,200 - root - INFO - fetch_positions:4078 - 活跃持仓: ETH/USDT, 类型: spot, 方向: long, 数量: 1.0, 入场价: 2453.4
2025-06-26 18:56:38,201 - root - INFO - fetch_positions:4083 - 获取持仓: 3个活跃持仓 (合约: 0, 现货: 3)
2025-06-26 18:56:38,202 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:56:38,203 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:56:38,204 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:56:38,205 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:56:38,207 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:56:40,104 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:56:40,105 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:56:40,106 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:56:40,110 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:56:44,515 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:56:44,516 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:56:44,517 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:56:44,518 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:56:44,519 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:56:46,195 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:56:46,196 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:56:46,197 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:56:46,201 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:56:50,823 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:56:50,825 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:56:50,826 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:56:50,827 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:56:50,828 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:56:52,547 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:56:52,548 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:56:52,550 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:56:52,554 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:56:57,191 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:56:57,192 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:56:57,193 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:56:57,194 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:56:57,195 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:56:58,866 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:56:58,867 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:56:58,868 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:56:58,872 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:57:03,478 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:57:03,479 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:57:03,480 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:57:03,481 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:57:03,482 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:57:05,506 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:57:05,508 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:57:05,956 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:57:05,960 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:57:09,869 - root - INFO - fetch_positions:4078 - 活跃持仓: BTC/USDT, 类型: spot, 方向: long, 数量: 1.0, 入场价: 107381.8
2025-06-26 18:57:09,870 - root - INFO - fetch_positions:4078 - 活跃持仓: OKB/USDT, 类型: spot, 方向: long, 数量: 100.0, 入场价: 50.08
2025-06-26 18:57:09,871 - root - INFO - fetch_positions:4078 - 活跃持仓: ETH/USDT, 类型: spot, 方向: long, 数量: 1.0, 入场价: 2453.32
2025-06-26 18:57:09,872 - root - INFO - fetch_positions:4083 - 获取持仓: 3个活跃持仓 (合约: 0, 现货: 3)
2025-06-26 18:57:09,873 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:57:09,874 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:57:09,875 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:57:09,876 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:57:09,877 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:57:11,421 - root - INFO - _generate_performance_report:3083 - 
=== 实时监控性能报告 ===
总持仓数量: 0
风险分布: 高风险(0) | 中风险(0) | 低风险(0)
投资组合风险: 0.000
平均盈亏: 0.00%
活跃持仓数: 0
智能优化状态:
  - 智能平仓优化: ✅ 启用
  - 智能开仓优化: ✅ 启用
  - 动态止损优化: ✅ 启用
========================

2025-06-26 18:57:11,842 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:57:11,843 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:57:11,844 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:57:11,849 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:57:12,904 - root - INFO - update_main_trading_table:2817 - 表格强制更新(第140次) - 性能优化版本
2025-06-26 18:57:16,505 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:57:16,506 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:57:16,507 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:57:16,508 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:57:16,509 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:57:18,185 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:57:18,186 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:57:18,187 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:57:18,189 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:57:22,826 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:57:22,827 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:57:22,828 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:57:22,829 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:57:22,830 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:57:24,799 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:57:24,799 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:57:24,800 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:57:24,801 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:57:29,960 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:57:29,961 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:57:29,962 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:57:29,963 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:57:29,964 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:57:31,622 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:57:31,623 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:57:31,624 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:57:31,628 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:57:36,490 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:57:36,491 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:57:36,492 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:57:36,493 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:57:36,494 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:57:38,296 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:57:38,298 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:57:38,299 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:57:38,303 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:57:42,881 - root - INFO - fetch_positions:4078 - 活跃持仓: BTC/USDT, 类型: spot, 方向: long, 数量: 1.0, 入场价: 107428.0
2025-06-26 18:57:42,882 - root - INFO - fetch_positions:4078 - 活跃持仓: OKB/USDT, 类型: spot, 方向: long, 数量: 100.0, 入场价: 50.07
2025-06-26 18:57:42,883 - root - INFO - fetch_positions:4078 - 活跃持仓: ETH/USDT, 类型: spot, 方向: long, 数量: 1.0, 入场价: 2452.3
2025-06-26 18:57:42,884 - root - INFO - fetch_positions:4083 - 获取持仓: 3个活跃持仓 (合约: 0, 现货: 3)
2025-06-26 18:57:42,885 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:57:42,886 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:57:42,887 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:57:42,889 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:57:42,890 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:57:44,539 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:57:44,541 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:57:44,542 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:57:44,546 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:57:49,267 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:57:49,268 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:57:49,269 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:57:49,270 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:57:49,271 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:57:51,390 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:57:51,391 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:57:51,392 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:57:51,396 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:57:55,871 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:57:55,872 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:57:55,873 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:57:55,875 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:57:55,876 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:57:57,939 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:57:57,940 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:57:57,940 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:57:57,942 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:58:03,334 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:58:03,335 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:58:03,336 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:58:03,337 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:58:03,338 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:58:05,022 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:58:05,023 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:58:05,025 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:58:05,029 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:58:09,729 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:58:09,730 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:58:09,731 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:58:09,732 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:58:09,733 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:58:11,391 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:58:11,392 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:58:11,811 - root - INFO - update_main_trading_table:2309 - 账户余额更新: 5000.00 USDT (缓存60s)
2025-06-26 18:58:11,812 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:58:11,816 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:58:16,159 - root - INFO - fetch_positions:4078 - 活跃持仓: BTC/USDT, 类型: spot, 方向: long, 数量: 1.0, 入场价: 107417.5
2025-06-26 18:58:16,161 - root - INFO - fetch_positions:4078 - 活跃持仓: OKB/USDT, 类型: spot, 方向: long, 数量: 100.0, 入场价: 50.11
2025-06-26 18:58:16,162 - root - INFO - fetch_positions:4078 - 活跃持仓: ETH/USDT, 类型: spot, 方向: long, 数量: 1.0, 入场价: 2452.36
2025-06-26 18:58:16,163 - root - INFO - fetch_positions:4083 - 获取持仓: 3个活跃持仓 (合约: 0, 现货: 3)
2025-06-26 18:58:16,164 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:58:16,165 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:58:16,166 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:58:16,167 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:58:16,168 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:58:17,415 - root - INFO - trade_loop:2144 - 内存使用: 205.6MB, 循环计数: 150
2025-06-26 18:58:17,831 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:58:17,833 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:58:17,834 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:58:17,838 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:58:22,792 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:58:22,793 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:58:22,794 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:58:22,795 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:58:22,796 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:58:24,310 - strategy_core - ERROR - wrapper:513 - 策略执行失败: select_product.选品策略一, 错误: select_symbols_by_atr_adx() got an unexpected keyword argument 'min_adx'
2025-06-26 18:58:24,313 - root - ERROR - execute_select_product_strategy:1119 - 执行ATR/ADX策略失败: An asyncio.Future, a coroutine or an awaitable is required
2025-06-26 18:58:24,314 - root - INFO - trade_loop:1797 - 智能选品策略 选品策略一 选出的可开仓候选: ['BTC/USDT:USDT', 'ETH/USDT:USDT', 'BNB/USDT:USDT']
2025-06-26 18:58:24,749 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:58:24,750 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:58:24,751 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:58:24,755 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:58:29,532 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:58:29,532 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:58:29,533 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:58:29,533 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:58:29,534 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:58:31,206 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:58:31,206 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:58:31,207 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:58:31,209 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:58:35,883 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:58:35,884 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:58:35,885 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:58:35,885 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:58:35,886 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:58:37,560 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:58:37,561 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:58:37,563 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:58:37,567 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:58:42,196 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:58:42,196 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:58:42,197 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:58:42,197 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:58:42,198 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:58:44,004 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:58:44,005 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:58:44,006 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:58:44,010 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:58:49,302 - root - INFO - fetch_positions:4078 - 活跃持仓: BTC/USDT, 类型: spot, 方向: long, 数量: 1.0, 入场价: 107424.7
2025-06-26 18:58:49,303 - root - INFO - fetch_positions:4078 - 活跃持仓: OKB/USDT, 类型: spot, 方向: long, 数量: 100.0, 入场价: 50.12
2025-06-26 18:58:49,304 - root - INFO - fetch_positions:4078 - 活跃持仓: ETH/USDT, 类型: spot, 方向: long, 数量: 1.0, 入场价: 2452.6
2025-06-26 18:58:49,305 - root - INFO - fetch_positions:4083 - 获取持仓: 3个活跃持仓 (合约: 0, 现货: 3)
2025-06-26 18:58:49,306 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:58:49,307 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:58:49,308 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:58:49,310 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:58:49,311 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:58:51,230 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:58:51,231 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:58:51,233 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:58:51,237 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:58:55,884 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:58:55,886 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:58:55,887 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:58:55,888 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:58:55,889 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:58:57,606 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:58:57,607 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:58:57,609 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:58:57,613 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:59:02,460 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:59:02,461 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:59:02,462 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:59:02,463 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:59:02,464 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:59:04,135 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:59:04,136 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:59:04,137 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:59:04,142 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:59:08,716 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:59:08,718 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:59:08,719 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:59:08,720 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:59:08,721 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:59:10,395 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:59:10,396 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:59:10,396 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:59:10,398 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:59:15,130 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:59:15,131 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:59:15,132 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:59:15,133 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:59:15,134 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:59:17,023 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:59:17,024 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:59:17,442 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:59:17,446 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:59:21,922 - root - INFO - fetch_positions:4078 - 活跃持仓: BTC/USDT, 类型: spot, 方向: long, 数量: 1.0, 入场价: 107423.1
2025-06-26 18:59:21,924 - root - INFO - fetch_positions:4078 - 活跃持仓: OKB/USDT, 类型: spot, 方向: long, 数量: 100.0, 入场价: 50.12
2025-06-26 18:59:21,925 - root - INFO - fetch_positions:4078 - 活跃持仓: ETH/USDT, 类型: spot, 方向: long, 数量: 1.0, 入场价: 2451.9
2025-06-26 18:59:21,926 - root - INFO - fetch_positions:4083 - 获取持仓: 3个活跃持仓 (合约: 0, 现货: 3)
2025-06-26 18:59:21,927 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:59:21,928 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:59:21,929 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:59:21,930 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:59:21,931 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:59:23,178 - root - INFO - _generate_performance_report:3083 - 
=== 实时监控性能报告 ===
总持仓数量: 0
风险分布: 高风险(0) | 中风险(0) | 低风险(0)
投资组合风险: 0.000
平均盈亏: 0.00%
活跃持仓数: 0
智能优化状态:
  - 智能平仓优化: ✅ 启用
  - 智能开仓优化: ✅ 启用
  - 动态止损优化: ✅ 启用
========================

2025-06-26 18:59:23,598 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:59:23,600 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:59:23,601 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:59:23,605 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:59:24,612 - root - INFO - update_main_trading_table:2817 - 表格强制更新(第160次) - 性能优化版本
2025-06-26 18:59:28,254 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:59:28,255 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:59:28,256 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:59:28,257 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:59:28,258 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:59:29,915 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:59:29,917 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:59:29,918 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:59:29,922 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:59:35,210 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:59:35,211 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:59:35,212 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:59:35,213 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:59:35,214 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:59:37,223 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:59:37,224 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:59:37,225 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:59:37,229 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:59:41,842 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:59:41,843 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:59:41,843 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:59:41,843 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:59:41,844 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:59:43,502 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:59:43,503 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:59:43,504 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:59:43,508 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:59:48,352 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:59:48,354 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:59:48,355 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:59:48,356 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:59:48,357 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:59:50,009 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:59:50,010 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:59:50,011 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:59:50,015 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 18:59:54,732 - root - INFO - fetch_positions:4078 - 活跃持仓: BTC/USDT, 类型: spot, 方向: long, 数量: 1.0, 入场价: 107407.5
2025-06-26 18:59:54,733 - root - INFO - fetch_positions:4078 - 活跃持仓: OKB/USDT, 类型: spot, 方向: long, 数量: 100.0, 入场价: 50.12
2025-06-26 18:59:54,734 - root - INFO - fetch_positions:4078 - 活跃持仓: ETH/USDT, 类型: spot, 方向: long, 数量: 1.0, 入场价: 2450.7
2025-06-26 18:59:54,735 - root - INFO - fetch_positions:4083 - 获取持仓: 3个活跃持仓 (合约: 0, 现货: 3)
2025-06-26 18:59:54,736 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 18:59:54,737 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 18:59:54,738 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 18:59:54,739 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 18:59:54,740 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 18:59:56,655 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 18:59:56,657 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 18:59:56,658 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 18:59:56,662 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 19:00:01,567 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 19:00:01,568 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 19:00:01,568 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 19:00:01,569 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 19:00:01,569 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 19:00:03,803 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 19:00:03,805 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 19:00:03,806 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 19:00:03,810 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 19:00:08,508 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 19:00:08,509 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 19:00:08,510 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 19:00:08,511 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 19:00:08,512 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 19:00:10,160 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 19:00:10,161 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 19:00:10,163 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 19:00:10,167 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 19:00:15,014 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 19:00:15,015 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 19:00:15,016 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 19:00:15,017 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 19:00:15,018 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 19:00:16,692 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 19:00:16,693 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 19:00:16,694 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 19:00:16,699 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 19:00:21,421 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 19:00:21,422 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 19:00:21,423 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 19:00:21,424 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 19:00:21,425 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 19:00:23,131 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 19:00:23,132 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 19:00:23,549 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 19:00:23,554 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 19:00:27,879 - root - INFO - fetch_positions:4078 - 活跃持仓: BTC/USDT, 类型: spot, 方向: long, 数量: 1.0, 入场价: 107395.3
2025-06-26 19:00:27,879 - root - INFO - fetch_positions:4078 - 活跃持仓: OKB/USDT, 类型: spot, 方向: long, 数量: 100.0, 入场价: 50.14
2025-06-26 19:00:27,880 - root - INFO - fetch_positions:4078 - 活跃持仓: ETH/USDT, 类型: spot, 方向: long, 数量: 1.0, 入场价: 2450.71
2025-06-26 19:00:27,880 - root - INFO - fetch_positions:4083 - 获取持仓: 3个活跃持仓 (合约: 0, 现货: 3)
2025-06-26 19:00:27,881 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 19:00:27,881 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.000s)
2025-06-26 19:00:27,881 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 19:00:27,882 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 19:00:27,882 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 19:00:29,539 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 19:00:29,540 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 19:00:29,541 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 19:00:29,545 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 19:00:34,229 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 19:00:34,230 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 19:00:34,231 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 19:00:34,232 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 19:00:34,233 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 19:00:35,907 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 19:00:35,908 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 19:00:35,909 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 19:00:35,913 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 19:00:40,568 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 19:00:40,569 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 19:00:40,570 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 19:00:40,571 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 19:00:40,572 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 19:00:42,251 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 19:00:42,252 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 19:00:42,252 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 19:00:42,254 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 19:00:47,093 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 19:00:47,095 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 19:00:47,096 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 19:00:47,097 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 19:00:47,098 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 19:00:48,826 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 19:00:48,827 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 19:00:48,827 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 19:00:48,829 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 19:00:53,444 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 19:00:53,445 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 19:00:53,447 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 19:00:53,448 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 19:00:53,449 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 19:00:55,232 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 19:00:55,233 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 19:00:55,233 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 19:00:55,235 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 19:00:59,881 - root - INFO - fetch_positions:4078 - 活跃持仓: BTC/USDT, 类型: spot, 方向: long, 数量: 1.0, 入场价: 107374.5
2025-06-26 19:00:59,883 - root - INFO - fetch_positions:4078 - 活跃持仓: OKB/USDT, 类型: spot, 方向: long, 数量: 100.0, 入场价: 50.1
2025-06-26 19:00:59,884 - root - INFO - fetch_positions:4078 - 活跃持仓: ETH/USDT, 类型: spot, 方向: long, 数量: 1.0, 入场价: 2450.64
2025-06-26 19:00:59,885 - root - INFO - fetch_positions:4083 - 获取持仓: 3个活跃持仓 (合约: 0, 现货: 3)
2025-06-26 19:00:59,886 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 19:00:59,887 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 19:00:59,888 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 19:00:59,889 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 19:00:59,890 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 19:01:01,562 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 19:01:01,563 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 19:01:01,563 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 19:01:01,565 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 19:01:06,538 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 19:01:06,539 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 19:01:06,540 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 19:01:06,541 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 19:01:06,542 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 19:01:09,337 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 19:01:09,337 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 19:01:09,338 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 19:01:09,341 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 19:01:13,763 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 19:01:13,764 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 19:01:13,765 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 19:01:13,766 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 19:01:13,767 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 19:01:15,126 - strategy_core - ERROR - wrapper:513 - 策略执行失败: select_product.选品策略一, 错误: select_symbols_by_atr_adx() got an unexpected keyword argument 'min_adx'
2025-06-26 19:01:15,128 - root - ERROR - execute_select_product_strategy:1119 - 执行ATR/ADX策略失败: An asyncio.Future, a coroutine or an awaitable is required
2025-06-26 19:01:15,129 - root - INFO - trade_loop:1797 - 智能选品策略 选品策略一 选出的可开仓候选: ['BTC/USDT:USDT', 'ETH/USDT:USDT', 'BNB/USDT:USDT']
2025-06-26 19:01:15,594 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 19:01:15,596 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 19:01:15,597 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 19:01:15,601 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
2025-06-26 19:01:20,192 - root - INFO - _synchronize_status_details_with_live_positions:1543 - SYNC_STATUS: 开始同步 3 个实时持仓
2025-06-26 19:01:20,193 - root - INFO - _synchronize_status_details_with_live_positions:1565 - SYNC_STATUS: 同步完成 - 处理 3 个持仓 (耗时 0.001s)
2025-06-26 19:01:20,194 - root - INFO - _synchronize_status_details_with_live_positions:1566 - SYNC_STATUS: 详细统计 - 新发现: 0, API增强: 3
2025-06-26 19:01:20,194 - root - INFO - _synchronize_status_details_with_live_positions:1573 - SYNC_STATUS: 当前 trade_status_details 包含 6 个品种
2025-06-26 19:01:20,195 - root - INFO - trade_loop:1680 - 当前活跃持仓 (3个): ['BTC/USDT', 'OKB/USDT', 'ETH/USDT']
2025-06-26 19:01:21,834 - root - INFO - update_main_trading_table:2268 - UI_UPDATE: 实际持仓数量: 3, 程序状态数量: 6, 总显示数量: 9
2025-06-26 19:01:21,834 - root - INFO - update_main_trading_table:2270 - UI_UPDATE: 实际持仓品种: ['positions', 'contract_count', 'spot_count']
2025-06-26 19:01:21,835 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 positions: 'list' object has no attribute 'get'
2025-06-26 19:01:21,837 - root - ERROR - update_main_trading_table:2730 - 处理品种数据时出错 spot_count: 'int' object has no attribute 'get'
