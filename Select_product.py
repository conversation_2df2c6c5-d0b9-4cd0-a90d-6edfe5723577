# contract_symbols.py
import asyncio
import talib
import numpy as np
from typing import List
from strategy_core import strategy_manager, performance_monitor, get_strategy_config

async def fetch_ohlcv_async(exchange, symbol, timeframe='1h', limit=100):
    """异步获取K线数据"""
    try:
        # ccxt的异步方法需要不同的调用方式
        if exchange.has['fetchOHLCV']:
            # 这是一个示例，具体需要根据ccxtpro或特定交易所的异步支持来调整
            # 此处我们用同步方法模拟异步，实际应使用 aiohttp 等库配合 ccxt.pro
            loop = asyncio.get_event_loop()
            ohlcv = await loop.run_in_executor(None, exchange.fetch_ohlcv, symbol, timeframe, None, limit)
            return symbol, ohlcv
    except Exception as e:
        # logging.warning(f"异步获取 {symbol} K线失败: {e}") # 减少日志
        return symbol, None

@performance_monitor("select_product", "选品策略一")
async def select_symbols_by_atr_adx(exchange, symbols, atr_threshold=0.05, adx_threshold=25, atr_period=14):
    """
    选品策略一：基于ATR和ADX技术指标的选品策略
    - ATR (Average True Range) 用于衡量波动性。
    - ADX (Average Directional Movement Index) 用于衡量趋势强度。
    选择标准：波动性适中（ATR不太高也不太低），且趋势明显（ADX较高）。
    """
    import logging

    logging.info(f"执行选品策略二：ATR阈值={atr_threshold}, ADX阈值={adx_threshold}")
    
    tasks = [fetch_ohlcv_async(exchange, symbol) for symbol in symbols]
    results = await asyncio.gather(*tasks)
    
    selected_symbols = []
    for symbol, ohlcv in results:
        if ohlcv is None or len(ohlcv) < 20: # 需要足够数据来计算指标
            continue
        
        try:
            # 转换数据为numpy数组
            high = np.array([float(x[2]) for x in ohlcv])
            low = np.array([float(x[3]) for x in ohlcv])
            close = np.array([float(x[4]) for x in ohlcv])
            
            # 计算ATR和ADX
            atr = talib.ATR(high, low, close, timeperiod=atr_period)[-1]
            adx = talib.ADX(high, low, close, timeperiod=atr_period)[-1]
            
            # 标准化ATR，使其具有可比性
            normalized_atr = atr / close[-1]
            
            logging.debug(f"{symbol}: ATR={atr:.4f}, Normalized ATR={normalized_atr:.4f}, ADX={adx:.2f}")
            
            # 筛选条件
            if normalized_atr > atr_threshold and adx > adx_threshold:
                selected_symbols.append(symbol)
                logging.info(f"选中 {symbol} (ATR={normalized_atr:.4f}, ADX={adx:.2f})")

        except Exception as e:
            logging.error(f"计算 {symbol} 指标时出错: {e}")
            continue
            
    strategy_name = "ATR/ADX趋势波动策略"
    logging.info(f"策略 '{strategy_name}' 选择完成，选出 {len(selected_symbols)} 个品种: {selected_symbols}")
    return selected_symbols, strategy_name

# 用于选取5-10个主流合约交易品种
# 可根据实时行情动态调整

RESTRICTED_SYMBOLS = [] # 不需要限制的品种列表，可根据需要添加，例如 ["BTC/USDT:USDT", "ETH/USDT:USDT"]
# =====================================================
# 智能品种选择：用于智能分析和选择最优交易品种
# =====================================================
@performance_monitor("select_product", "选品策略二")
async def smart_select_symbols_v2(exchange, symbols: List[str] = None, limit: int = 10, 
                                 min_volume_24h: float = 1000000, 
                                 min_price_change: float = 0.02,
                                 max_symbols: int = 15) -> List[str]:
    """
    选品策略二：智能综合评分选品策略
    基于市场条件智能选择最适合的交易品种
    优先选择流动性好、波动适中、价格合理的品种
    
    新增功能：
    - 多维度技术指标分析（RSI、MACD、布林带）
    - 市场情绪评估
    - 动态评分机制
    - 风险评估模块
    """
    import logging
    import random
    import pandas as pd
    import numpy as np
    from datetime import datetime, timedelta

    logging.info(f"V2 - 开始智能选择品种，目标数量: {limit}, 最小交易额: {min_volume_24h}, 最小价格变化: {min_price_change}")

    if exchange is None:
        logging.warning("V2 - 交易所对象为None，无法执行选择。")
        return [], "no_exchange"

    try:
        if not hasattr(exchange, 'markets') or not exchange.markets:
            exchange.load_markets()
        
        all_symbols = list(exchange.markets.keys())
        
        # 筛选出UDS本位的永续合约
        contract_symbols = [s for s in all_symbols if s.endswith(':USDT')]
        
        candidates = []
        for symbol in contract_symbols:
            try:
                ticker = exchange.fetch_ticker(symbol)
                
                # 过滤掉没有足够信息的品种
                if not all(k in ticker for k in ['last', 'quoteVolume', 'info']):
                    continue

                price = float(ticker['last'])
                volume_24h = float(ticker['quoteVolume'])
                
                # 价格和交易量过滤
                if not (min_price <= price <= max_price and volume_24h >= min_volume):
                    continue
                
                # 增加波动率分析
                ohlcv = exchange.fetch_ohlcv(symbol, '1h', limit=24)
                if len(ohlcv) < 24:
                    continue
                
                df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
                price_change = (df['close'].iloc[-1] / df['close'].iloc[0] - 1)
                
                if abs(price_change) < volatility_threshold:
                    continue

                # 综合评分
                score = (volume_24h / 1000000) + (abs(price_change) * 100) # 简单评分模型
                candidates.append({'symbol': symbol, 'score': score})

            except Exception as e:
                # logging.warning(f"V2 - 分析品种 {symbol} 时出错: {e}") # 减少不必要的日志
                continue

        if not candidates:
            logging.warning("V2 - 未找到符合条件的候选品种。")
            return [], "no_candidates"

        # 按评分排序并选择前N个
        sorted_candidates = sorted(candidates, key=lambda x: x['score'], reverse=True)
        selected_symbols = [c['symbol'] for c in sorted_candidates[:top_n]]
        
        strategy_name = "选品策略二"
        logging.info(f"V2 - 智能选择完成，选出 {len(selected_symbols)} 个品种: {selected_symbols}")
        logging.info(f"V2 - 使用策略 '{strategy_name}' 完成选择。")
        # 确保返回的是一个元组
        result = (selected_symbols, strategy_name)
        logging.info(f"V2 - 即将返回元组: {result} (类型: {type(result)})")
        return result

    except Exception as e:
        logging.error(f"V2 - 智能选择品种时发生严重错误: {e}")
        import traceback
        logging.error(traceback.format_exc())
        return [], "error_in_selection"
    """
    智能品种选择策略 v2 - 增强版
    基于市场条件智能选择最适合的交易品种
    优先选择流动性好、波动适中、价格合理的品种
    """
    import logging
    import random

    logging.info("执行智能品种选择：基于市场条件选择最适合的交易品种")

    # 如果exchange为None，直接返回默认品种
    if exchange is None:
        logging.warning("交易所对象为None，返回默认交易品种")
        return ["DOGE/USDT:USDT", "DOGE/USDT"]  # 使用DOGE，更稳定

    # 分层交易品种池 - 按优先级排序
    premium_symbols = {
        'contract': ['DOGE/USDT:USDT', 'SHIB/USDT:USDT', 'PEPE/USDT:USDT'],
        'spot': ['DOGE/USDT', 'SHIB/USDT', 'PEPE/USDT']
    }
    
    standard_symbols = {
        'contract': ['HOT/USDT:USDT', 'BTT/USDT:USDT', 'WIN/USDT:USDT', 'LUNC/USDT:USDT'],
        'spot': ['JASMY/USDT', 'HOT/USDT', 'BTT/USDT', 'WIN/USDT']
    }
    
    backup_symbols = {
        'contract': ['XRP/USDT:USDT', 'ADA/USDT:USDT'],
        'spot': ['XRP/USDT', 'ADA/USDT']
    }

    try:
        # 确保市场数据已加载
        if not hasattr(exchange, 'markets') or not exchange.markets:
            exchange.load_markets()
        
        selected_symbols = []
        
        # 增强的智能选择函数
        def evaluate_symbol_advanced(symbol):
            """增强版品种评估 - 多维度技术分析"""
            try:
                if symbol not in exchange.markets:
                    return None, 0, {}
                
                ticker = exchange.fetch_ticker(symbol)
                if not ticker:
                    return None, 0, {}
                
                price = ticker.get('last', 0)
                volume = ticker.get('quoteVolume', 0)
                price_change_24h = ticker.get('percentage', 0)
                
                # 获取K线数据进行技术分析
                try:
                    ohlcv = exchange.fetch_ohlcv(symbol, '1h', limit=50)
                    if len(ohlcv) < 30:
                        return symbol, 0, {'reason': '数据不足'}
                    
                    df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
                    
                    # 计算技术指标
                    # 1. RSI指标
                    delta = df['close'].diff()
                    gain = delta.where(delta > 0, 0).rolling(window=14).mean()
                    loss = -delta.where(delta < 0, 0).rolling(window=14).mean()
                    rs = gain / loss
                    rsi = 100 - (100 / (1 + rs))
                    current_rsi = rsi.iloc[-1] if not pd.isna(rsi.iloc[-1]) else 50
                    
                    # 2. 移动平均线
                    ma_short = df['close'].rolling(window=7).mean()
                    ma_long = df['close'].rolling(window=25).mean()
                    ma_trend = (ma_short.iloc[-1] / ma_long.iloc[-1] - 1) * 100 if not pd.isna(ma_short.iloc[-1]) and not pd.isna(ma_long.iloc[-1]) else 0
                    
                    # 3. 布林带
                    bb_middle = df['close'].rolling(window=20).mean()
                    bb_std = df['close'].rolling(window=20).std()
                    bb_upper = bb_middle + 2 * bb_std
                    bb_lower = bb_middle - 2 * bb_std
                    bb_position = ((price - bb_lower.iloc[-1]) / (bb_upper.iloc[-1] - bb_lower.iloc[-1])) if (bb_upper.iloc[-1] - bb_lower.iloc[-1]) > 0 else 0.5
                    
                    # 4. 波动率分析
                    volatility = df['close'].rolling(window=20).std() / df['close'].rolling(window=20).mean()
                    current_volatility = volatility.iloc[-1] if not pd.isna(volatility.iloc[-1]) else 0
                    
                    # 5. 成交量趋势
                    volume_ma = df['volume'].rolling(window=10).mean()
                    volume_trend = (df['volume'].iloc[-5:].mean() / volume_ma.iloc[-1] - 1) * 100 if not pd.isna(volume_ma.iloc[-1]) and volume_ma.iloc[-1] > 0 else 0
                    
                except Exception as e:
                    logging.warning(f"技术分析 {symbol} 失败: {e}")
                    current_rsi = 50
                    ma_trend = 0
                    bb_position = 0.5
                    current_volatility = 0.02
                    volume_trend = 0
                
                # 多维度评分系统
                score = 0
                analysis_details = {}
                
                # 1. 基础流动性评分 (30分)
                if volume > 5000000:
                    liquidity_score = 30
                elif volume > 1000000:
                    liquidity_score = 25
                elif volume > 100000:
                    liquidity_score = 15
                else:
                    liquidity_score = 5
                score += liquidity_score
                analysis_details['liquidity_score'] = liquidity_score
                
                # 2. 价格合理性评分 (20分)
                if 0.001 <= price <= 1:
                    price_score = 20
                elif price <= 10:
                    price_score = 15
                elif price <= 100:
                    price_score = 10
                else:
                    price_score = 5
                score += price_score
                analysis_details['price_score'] = price_score
                
                # 3. 技术指标评分 (25分)
                tech_score = 0
                # RSI评分 (适中的RSI更好)
                if 40 <= current_rsi <= 60:
                    tech_score += 8
                elif 30 <= current_rsi <= 70:
                    tech_score += 5
                else:
                    tech_score += 2
                
                # 趋势评分
                if abs(ma_trend) < 1:  # 横盘整理
                    tech_score += 5
                elif ma_trend > 0:  # 上升趋势
                    tech_score += 7
                else:  # 下降趋势
                    tech_score += 3
                
                # 布林带位置评分
                if 0.3 <= bb_position <= 0.7:  # 中性位置
                    tech_score += 5
                elif bb_position < 0.2:  # 超卖
                    tech_score += 8
                elif bb_position > 0.8:  # 超买
                    tech_score += 3
                
                score += tech_score
                analysis_details['tech_score'] = tech_score
                
                # 4. 波动率评分 (15分)
                if 0.01 <= current_volatility <= 0.05:  # 适中波动
                    volatility_score = 15
                elif 0.005 <= current_volatility <= 0.08:
                    volatility_score = 10
                else:
                    volatility_score = 5
                score += volatility_score
                analysis_details['volatility_score'] = volatility_score
                
                # 5. 市场动量评分 (10分)
                momentum_score = 0
                if abs(price_change_24h) < 5:  # 稳定
                    momentum_score += 5
                if volume_trend > 0:  # 成交量增加
                    momentum_score += 5
                score += momentum_score
                analysis_details['momentum_score'] = momentum_score
                
                # 记录详细分析
                analysis_details.update({
                    'price': price,
                    'volume': volume,
                    'price_change_24h': price_change_24h,
                    'rsi': current_rsi,
                    'ma_trend': ma_trend,
                    'bb_position': bb_position,
                    'volatility': current_volatility,
                    'volume_trend': volume_trend,
                    'total_score': score
                })
                
                return symbol, score, analysis_details
                
            except Exception as e:
                logging.warning(f"评估 {symbol} 时出错: {e}")
                return None, 0, {'error': str(e)}
        
        # 按优先级选择品种
        symbol_pools = [premium_symbols, standard_symbols, backup_symbols]
        
        for pool in symbol_pools:
            if len(selected_symbols) >= 2:
                break
                
            # 评估合约品种
            if len(selected_symbols) == 0:
                contract_candidates = []
                for symbol in pool['contract']:
                    result, score, details = evaluate_symbol_advanced(symbol)
                    if result and score > 30:  # 最低分数要求
                        contract_candidates.append((result, score, details))
                
                if contract_candidates:
                    # 选择得分最高的合约品种，并记录分析详情
                    contract_candidates.sort(key=lambda x: x[1], reverse=True)
                    best_contract = contract_candidates[0]
                    selected_symbols.append(best_contract[0])
                    logging.info(f"选择合约品种: {best_contract[0]}, 得分: {best_contract[1]:.1f}")
                    if len(contract_candidates) > 0 and len(contract_candidates[0]) > 2:
                        logging.info(f"分析详情: {contract_candidates[0][2]}")
            
            # 评估现货品种
            if len(selected_symbols) == 1:
                spot_candidates = []
                for symbol in pool['spot']:
                    result, score, details = evaluate_symbol_advanced(symbol)
                    if result and score > 30:  # 最低分数要求
                        spot_candidates.append((result, score, details))
                
                if spot_candidates:
                    # 选择得分最高的现货品种，并记录分析详情
                    spot_candidates.sort(key=lambda x: x[1], reverse=True)
                    best_spot = spot_candidates[0]
                    selected_symbols.append(best_spot[0])
                    logging.info(f"选择现货品种: {best_spot[0]}, 得分: {best_spot[1]:.1f}")
                    logging.info(f"分析详情: {best_spot[2]}")
        
        # 如果仍然没有选到足够的品种，使用默认品种
        strategy_name = "smart_select_v2"
        if len(selected_symbols) < 2:
            logging.info("使用默认交易品种")
            default_symbols = ['DOGE/USDT:USDT', 'DOGE/USDT']
            return default_symbols, strategy_name
        
        logging.info(f"智能选择的交易品种: {selected_symbols[:2]}，策略: {strategy_name}")
        return selected_symbols[:2], strategy_name

        

    except Exception as e:
        logging.error(f"智能选择品种时发生错误: {e}")
        return ['DOGE/USDT:USDT', 'DOGE/USDT'], "default_fallback"

CONTRACT_SYMBOLS = [
    "BTC/USDT:USDT",
    "ETH/USDT:USDT",
    "SOL/USDT:USDT",
    "BNB/USDT:USDT",
    "DOGE/USDT:USDT",
    "XRP/USDT:USDT",
    "LINK/USDT:USDT",
    "AVAX/USDT:USDT",
    "DOT/USDT:USDT"
]

def get_trade_symbols(top_n=5):
    # 这里可扩展为根据行情、波动率、成交量等动态选币
    return CONTRACT_SYMBOLS[:top_n]

# 智能选择品种方法和持仓管理
import random
import logging
import time

def analyze_contract_performance(exchange, symbols):
    """
    分析合约品种的表现，选择盈利潜力最大的品种

    参数:
    - exchange: 交易所对象
    - symbols: 待分析的合约品种列表

    返回:
    - 按盈利潜力排序的品种列表
    """
    import pandas as pd
    import logging

    performance_data = []

    for symbol in symbols:
        try:
            # 获取历史K线数据
            ohlcv = exchange.fetch_ohlcv(symbol, '1h', limit=72)  # 获取72小时数据
            if len(ohlcv) < 24:
                logging.warning(f"{symbol} 历史数据不足，跳过分析")
                continue

            # 转换为DataFrame
            df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])

            # 计算基本指标
            # 1. 24小时价格变化率
            price_change_24h = (df['close'].iloc[-1] / df['close'].iloc[-24] - 1) * 100

            # 2. 24小时成交量变化率
            volume_change_24h = (df['volume'].iloc[-24:].mean() / df['volume'].iloc[-48:-24].mean() - 1) * 100

            # 3. 波动率 (24小时标准差/均价)
            volatility = df['close'].iloc[-24:].std() / df['close'].iloc[-24:].mean() * 100

            # 4. 趋势强度 (当前价格与24小时移动平均线的关系)
            ma24 = df['close'].iloc[-24:].mean()
            trend_strength = (df['close'].iloc[-1] / ma24 - 1) * 100

            # 5. 获取当前价格
            current_price = df['close'].iloc[-1]

            # 计算综合得分 (盈利潜力)
            # 正向因素: 价格上涨、成交量增加、适度波动、强上升趋势
            score = (
                price_change_24h * 0.4 +  # 价格变化权重40%
                volume_change_24h * 0.2 +  # 成交量变化权重20%
                (10 - abs(volatility - 5)) * 0.1 +  # 适度波动权重10% (理想波动率5%左右)
                trend_strength * 0.3  # 趋势强度权重30%
            )

            # 记录分析结果
            performance_data.append({
                'symbol': symbol,
                'price': current_price,
                'price_change_24h': price_change_24h,
                'volume_change_24h': volume_change_24h,
                'volatility': volatility,
                'trend_strength': trend_strength,
                'score': score
            })

            logging.info(f"合约分析 {symbol}: 价格={current_price}, 24h变化={price_change_24h:.2f}%, "
                         f"成交量变化={volume_change_24h:.2f}%, 波动率={volatility:.2f}%, "
                         f"趋势强度={trend_strength:.2f}%, 得分={score:.2f}")

        except Exception as e:
            logging.error(f"分析 {symbol} 表现失败: {str(e)}")

    # 按得分排序 (从高到低)
    performance_data.sort(key=lambda x: x['score'], reverse=True)

    # 返回排序后的品种列表
    return [item['symbol'] for item in performance_data]

def smart_select_symbols(exchange, current_positions=None):
    import logging
    import pandas as pd
    import time

    start_time = time.time()

    if current_positions is None:
        current_positions = []

    restricted_symbols = RESTRICTED_SYMBOLS

    try:
        # 获取所有活跃的USDT合约交易对
        # markets = exchange.load_markets() # 改为使用预加载的 exchange.markets
        if not hasattr(exchange, 'markets') or not exchange.markets:
            try:
                logging.info("exchange.markets 未加载，尝试在 smart_select_symbols 中加载...")
                exchange.load_markets(True) # 尝试加载
                logging.info("在 smart_select_symbols 中成功加载市场数据")
            except Exception as e_load_smart:
                logging.error(f"在 smart_select_symbols 中加载市场数据失败: {str(e_load_smart)}")
                # 兜底方案，返回主流币中未持仓的
                default_symbols = ["BTC/USDT:USDT", "ETH/USDT:USDT", "SOL/USDT:USDT"]
                available_defaults = [s_def for s_def in default_symbols if s_def not in (current_positions or [])]
                logging.info(f"智能选择因市场数据加载失败，返回默认可用品种: {available_defaults}")
                return available_defaults

        markets = exchange.markets # 使用预加载的市场数据
        candidate_symbols_with_volume = []
        for symbol_key, market_data in markets.items():
            if ':USDT' in symbol_key and market_data.get('active', False) and market_data.get('swap', False): # 确保是活跃的USDT本位合约
                # 排除已持仓和受限的币种
                if symbol_key not in current_positions and not any(r in symbol_key for r in restricted_symbols):
                    # 尝试从 market_data['info'] 获取24小时交易额(quoteVolume)
                    # OKX API v5 中合约的交易额字段可能是 'volCcy24h' (交易额，以计价货币为单位) 或 'vol24h' (交易量，以张或币为单位)
                    # 我们需要以USDT计价的交易额
                    quote_volume_24h = 0
                    if 'info' in market_data and isinstance(market_data['info'], dict):
                        # 优先使用 volCcy24h (计价货币交易额)
                        if market_data['info'].get('volCcy24h'):
                            try:
                                quote_volume_24h = float(market_data['info']['volCcy24h'])
                            except ValueError:
                                pass
                        # 备用：如果 volCcy24h 不可用或为0，尝试使用 vol24h (基础货币交易量) * 当前价格 (如果能快速获取)
                        # 但在这一步避免API调用，所以主要依赖 volCcy24h
                    
                    candidate_symbols_with_volume.append({
                        'symbol': symbol_key,
                        'quoteVolume24h': quote_volume_24h
                    })

        if not candidate_symbols_with_volume:
            logging.warning("没有可用的合约交易品种进行初步筛选。")
            return []

        # 按24小时交易额降序排序
        candidate_symbols_with_volume.sort(key=lambda x: x['quoteVolume24h'], reverse=True)
        
        # 选择交易额最高的N个品种进行下一步分析 (例如前50个)
        top_n_candidates = [item['symbol'] for item in candidate_symbols_with_volume[:50]]

        if not top_n_candidates:
            logging.warning("按交易额筛选后没有候选品种。")
            return []
        
        logging.info(f"按交易额初步筛选出 {len(top_n_candidates)} 个候选合约: {top_n_candidates[:10]}...")

        performance_data = []
        
        # 批量获取 Tickers 信息
        tickers_data = {}
        try:
            if top_n_candidates:
                tickers_data = exchange.fetch_tickers(top_n_candidates)
        except Exception as e_fetch_tickers:
            logging.error(f"批量获取tickers失败: {str(e_fetch_tickers)}")
            # 如果批量获取失败，可以尝试逐个获取，或者直接放弃这些品种

        for symbol in top_n_candidates:
            try:
                ticker = tickers_data.get(symbol)
                if not ticker:
                    # 如果批量获取中没有这个品种的数据（可能因为某些原因失败），尝试单独获取一次
                    logging.warning(f"批量tickers中未找到 {symbol}，尝试单独获取...")
                    try:
                        ticker = exchange.fetch_ticker(symbol)
                    except Exception as e_single_ticker:
                        logging.error(f"单独获取ticker {symbol} 失败: {str(e_single_ticker)}")
                        continue # 获取失败则跳过此品种

                current_price = float(ticker['last'])
                price_change_24h = float(ticker.get('percentage', 0.0)) # 24小时价格变化百分比

                # （可选）对少数几个顶级候选获取K线进行短期动量分析
                # 为了减少API调用，暂时先基于24小时变化率和交易量（已用于排序）进行评分
                # 如果需要更精细的短期动量，可以在这里对例如前5名的品种获取1m K线
                
                score = price_change_24h # 简单以24小时涨幅为主要评分依据
                # 可以加入更多评分维度，例如：
                # score += candidate_symbols_with_volume_map[symbol]['quoteVolume24h'] * 0.0000001 # 交易量加权 (系数需要调整)

                performance_data.append({
                    'symbol': symbol,
                    'price': current_price,
                    'price_change_24h': price_change_24h,
                    'score': score
                })
                # logging.info(f"实时分析 {symbol}: 价格={current_price}, 24h变化={price_change_24h:.2f}%, 得分={score:.2f}")

            except Exception as e:
                logging.warning(f"分析 {symbol} 失败 (在ticker处理阶段): {str(e)}")
                performance_data.append({
                    'symbol': symbol,
                    'price': 0,
                    'price_change_24h': 0,
                    'score': -float('inf') # 分析失败的品种得分设为负无穷
                })

        # 按得分排序 (从高到低)
        performance_data.sort(key=lambda x: x['score'], reverse=True)

        ranked_symbols = [item['symbol'] for item in performance_data if item['score'] > -float('inf')] # 排除分析彻底失败的
        logging.info(f"智能选择品种完成，按潜力排序 (前10): {ranked_symbols[:10]}")

        execution_time = time.time() - start_time
        logging.info(f"智能选择品种完成，耗时: {execution_time:.2f}秒")

        return ranked_symbols

    except Exception as e:
        logging.error(f"智能选择品种失败: {str(e)}")
        execution_time = time.time() - start_time
        logging.warning(f"智能选择品种失败，耗时: {execution_time:.2f}秒")

        # 兜底方案，返回主流币中未持仓的
        default_symbols = ["BTC/USDT:USDT", "ETH/USDT:USDT", "SOL/USDT:USDT"]
        available_defaults = [s for s in default_symbols if s not in current_positions]
        logging.info(f"智能选择失败，返回默认可用品种: {available_defaults}")
        return available_defaults

@performance_monitor("select_product", "选品策略三")
def select_positions_to_close(exchange, positions, max_positions=20, num_to_close=3):
    """
    选品策略三：持仓平仓优选策略
    优化版：更全面的评估持仓表现，确保平仓最差的品种

    参数:
    - exchange: 交易所对象
    - positions: 当前持仓信息的字典，格式为 {symbol: position_info}
    - max_positions: 最大持仓数量，默认为20
    - num_to_close: 需要平仓的数量，默认为3

    返回:
    - 需要平仓的品种列表
    """
    # 如果持仓数量未达到上限，也可以平仓表现最差的品种
    # 修改为：只要持仓数量大于等于num_to_close，就可以平仓
    if len(positions) < num_to_close:
        logging.info(f"当前持仓数量({len(positions)})小于需要平仓的数量({num_to_close})，不需要平仓")
        return []

    try:
        # 计算每个持仓的收益率和其他指标
        position_pnl = []

        for symbol, position in positions.items():
            try:
                # 获取当前价格
                ticker = exchange.fetch_ticker(symbol)
                current_price = ticker['last']

                # 获取持仓信息
                entry_price = float(position.get('entryPrice', 0))
                position_side = position.get('side', 'long')  # 'long' 或 'short'

                # 获取持仓数量和杠杆
                contracts = float(position.get('contracts', 0))
                leverage = float(position.get('leverage', 10))  # 默认10倍杠杆

                # 计算收益率
                if entry_price > 0:
                    if position_side == 'long':
                        pnl_percentage = (current_price / entry_price - 1) * 100
                    else:  # short
                        pnl_percentage = (entry_price / current_price - 1) * 100
                else:
                    pnl_percentage = 0

                # 获取持仓时间
                entry_time = position.get('timestamp', 0) / 1000 if 'timestamp' in position else 0
                holding_time = time.time() - entry_time if entry_time > 0 else 0

                # 获取历史K线数据，分析趋势
                try:
                    ohlcv = exchange.fetch_ohlcv(symbol, '15m', limit=20)  # 获取最近20根15分钟K线
                    if len(ohlcv) >= 10:
                        # 计算短期趋势
                        recent_prices = [candle[4] for candle in ohlcv[-10:]]  # 最近10根K线的收盘价
                        price_trend = (recent_prices[-1] / recent_prices[0] - 1) * 100  # 趋势百分比变化
                    else:
                        price_trend = 0
                except Exception:
                    price_trend = 0

                # 计算资金占用
                margin = entry_price * contracts / leverage if leverage > 0 else 0

                # 记录信息
                position_pnl.append({
                    'symbol': symbol,
                    'pnl_percentage': pnl_percentage,
                    'holding_time': holding_time,
                    'entry_price': entry_price,
                    'current_price': current_price,
                    'position_side': position_side,
                    'price_trend': price_trend,
                    'margin': margin,
                    'contracts': contracts
                })

                logging.info(f"持仓 {symbol} 收益率: {pnl_percentage:.2f}%, 持仓时间: {holding_time/3600:.1f}小时, "
                             f"趋势: {price_trend:.2f}%, 保证金: {margin:.2f} USDT")

            except Exception as e:
                logging.error(f"计算 {symbol} 收益率失败: {str(e)}")
                # 如果计算失败，假设收益为0
                position_pnl.append({
                    'symbol': symbol,
                    'pnl_percentage': 0,
                    'holding_time': 0,
                    'entry_price': 0,
                    'current_price': 0,
                    'position_side': 'unknown',
                    'price_trend': 0,
                    'margin': 0,
                    'contracts': 0
                })

        # 如果没有持仓信息，返回空列表
        if not position_pnl:
            return []

        # 计算综合评分（考虑多种因素）
        for pos in position_pnl:
            # 1. 收益率评分：亏损的权重高，盈利的权重低
            if pos['pnl_percentage'] < 0:
                pnl_score = abs(pos['pnl_percentage']) * 2  # 亏损加倍计算
            else:
                pnl_score = -pos['pnl_percentage'] * 0.5  # 盈利减半计算（负分，越低越好）

            # 2. 持仓时间评分：持仓时间越长，权重越高
            time_score = min(pos['holding_time'] / 86400, 5) * 10  # 最多考虑5天，每天10分
            
            # 3. 趋势评分：下降趋势加分，上升趋势减分
            if pos['price_trend'] < 0:
                trend_score = abs(pos['price_trend']) * 0.5  # 下降趋势加分
            else:
                trend_score = -pos['price_trend'] * 0.2  # 上升趋势减分

            # 4. 资金占用评分：占用资金越多，平仓优先级越高
            margin_score = min(pos['margin'] / 100, 10) * 0.5  # 最多5分

            # 5. 特殊情况：严重亏损的立即平仓
            emergency_score = 50 if pos['pnl_percentage'] < -10 else 0  # 亏损超过10%加50分

            # 综合评分：各项得分加权求和，分数越高越应该平仓
            pos['close_score'] = (
                pnl_score * 0.4 +       # 收益率权重40%
                time_score * 0.2 +      # 持仓时间权重20%
                trend_score * 0.2 +     # 趋势权重20%
                margin_score * 0.2 +    # 资金占用权重20%
                emergency_score         # 紧急情况加分
            )

            logging.info(f"平仓评分 {pos['symbol']}: 总分={pos['close_score']:.2f}, "
                         f"收益={pnl_score:.2f}, 时间={time_score:.2f}, "
                         f"趋势={trend_score:.2f}, 资金={margin_score:.2f}")

        # 按评分排序（从高到低）
        position_pnl.sort(key=lambda x: x['close_score'], reverse=True)

        # 选择评分最高的前num_to_close个（表现最差的）
        to_close = [pos['symbol'] for pos in position_pnl[:num_to_close]]

        logging.info(f"选择平仓的品种: {to_close}")
        return to_close

    except Exception as e:
        logging.error(f"选择平仓品种失败: {str(e)}")
        # 如果选择失败，返回空列表
        return []

@performance_monitor("select_product", "选品策略四")
async def smart_select_symbols(exchange, symbols: List[str] = None, limit: int = 10) -> List[str]:
    """
    选品策略四：快速默认选品策略（向后兼容接口）
    选择可交易的一个合约和一个现货品种，用于智能交易系统
    
    参数:
    - exchange: 交易所对象，如果为None则返回默认品种

    返回:
    - [合约品种, 现货品种] 格式的列表
    """

    import logging

    # 如果没有提供exchange对象，返回默认品种
    if exchange is None:
        logging.warning("未提供exchange对象，返回默认交易品种")
        return ["DOGE/USDT:USDT", "DOGE/USDT"]

    # 添加额外的日志记录
    logging.info("smart_select_symbols被调用，将调用v2版本")

    # 调用v2版本的智能选择策略
    try:
        result = smart_select_symbols_v2(exchange)
        logging.info(f"smart_select_symbols_v2返回结果: {result}")
        return result
    except Exception as e:
        logging.error(f"调用smart_select_symbols_v2失败: {str(e)}")
        # 如果调用失败，返回默认品种
        return ["DOGE/USDT:USDT", "DOGE/USDT"]
