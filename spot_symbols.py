# spot_symbols.py
# 用于选取主流现货交易品种
# 可根据实时行情动态调整
import logging
import random
import time
import pandas as pd

# 主流现货交易品种列表
SPOT_SYMBOLS = [
    "BTC/USDT",
    "ETH/USDT",
    "SOL/USDT",
    "BNB/USDT",
    "DOGE/USDT",
    "XRP/USDT",
    "LINK/USDT",
    "AVAX/USDT",
    "MATIC/USDT",
    "DOT/USDT",
    "ADA/USDT",
    "LTC/USDT",
    "UNI/USDT",
    "ATOM/USDT",
    "ETC/USDT",
    "FIL/USDT",
    "NEAR/USDT",
    "APT/USDT",
    "ARB/USDT",
    "OP/USDT"
]

# 已知有交易限制的币种
RESTRICTED_SPOT_SYMBOLS = [
    "TON/USDT",
    "AUCTION/USDT",
    "VRA/USDT",
    "BAL/USDT",
    "PEOPLE/USDT",
    "MASK/USDT",
    "LUNA/USDT",
    "LUNC/USDT"
]

def get_spot_symbols(top_n=5):
    """获取前N个现货交易品种"""
    return SPOT_SYMBOLS[:top_n]

def smart_select_spot_symbols(exchange, current_positions=None, count=3):
    """
    智能选择现货交易品种，考虑当前持仓情况，每次选择指定数量的新品种
    
    参数:
    - exchange: 交易所对象
    - current_positions: 当前持仓的品种列表，默认为None（表示没有持仓）
    - count: 需要选择的新品种数量，默认为3
    
    返回:
    - 选择的品种列表
    """
    # 记录函数开始时间，用于性能监控
    start_time = time.time()
    
    # 如果current_positions为None，初始化为空列表
    if current_positions is None:
        current_positions = []
    
    try:
        # 随机打乱推荐列表，增加选择的随机性
        recommended_symbols = SPOT_SYMBOLS.copy()
        random.shuffle(recommended_symbols)
        
        # 过滤掉已持仓和受限的币种
        filtered_recommended = [s for s in recommended_symbols
                               if s not in current_positions
                               and not any(r in s for r in RESTRICTED_SPOT_SYMBOLS)]
        
        # 如果过滤后的推荐列表足够，直接从中选择
        if len(filtered_recommended) >= count:
            # 随机选择count个币种
            selected = random.sample(filtered_recommended, count)
            logging.info(f"从推荐现货列表中随机选择了{count}个币种: {selected}")
            
            # 记录执行时间
            execution_time = time.time() - start_time
            logging.info(f"智能选择现货品种完成，耗时: {execution_time:.2f}秒")
            
            return selected
        
        # 如果推荐列表不够，需要从市场中查找更多币种
        logging.info("推荐现货列表不足，从市场中查找更多币种")
        
        # 加载市场数据
        markets = exchange.load_markets()
        
        # 过滤出活跃的USDT现货，并排除已知限制的币种和当前持仓的币种
        available_symbols = []
        for symbol in markets:
            if '/USDT' in symbol and not ':' in symbol and markets[symbol]['active']:
                # 检查是否在限制列表中
                if any(restricted in symbol for restricted in RESTRICTED_SPOT_SYMBOLS):
                    continue
                # 检查是否已经在持仓中
                if symbol in current_positions:
                    continue
                available_symbols.append(symbol)
        
        # 随机打乱可用币种列表
        random.shuffle(available_symbols)
        
        # 如果可用币种足够，直接选择
        if len(available_symbols) >= count:
            selected = available_symbols[:count]
            logging.info(f"从可用现货币种中选择了{count}个币种: {selected}")
            
            # 记录执行时间
            execution_time = time.time() - start_time
            logging.info(f"智能选择现货品种完成，耗时: {execution_time:.2f}秒")
            
            return selected
        
        # 如果可用币种不足，使用所有可用的
        logging.warning(f"可用现货币种不足，只找到{len(available_symbols)}个")
        
        # 记录执行时间
        execution_time = time.time() - start_time
        logging.info(f"智能选择现货品种完成，耗时: {execution_time:.2f}秒")
        
        return available_symbols
        
    except Exception as e:
        logging.error(f"智能选择现货品种失败: {str(e)}")
        # 记录执行时间
        execution_time = time.time() - start_time
        logging.warning(f"智能选择现货品种失败，耗时: {execution_time:.2f}秒")
        
        # 兜底方案，返回主流币中未持仓的
        default_symbols = ["BTC/USDT", "ETH/USDT", "SOL/USDT", "DOGE/USDT", "XRP/USDT"]
        available_defaults = [s for s in default_symbols if s not in current_positions]
        
        # 随机打乱默认列表
        random.shuffle(available_defaults)
        
        if len(available_defaults) >= count:
            return available_defaults[:count]
        else:
            # 如果默认列表中的币种不够，随机选择一些其他推荐币种
            remaining = count - len(available_defaults)
            other_recommended = [s for s in recommended_symbols
                               if s not in default_symbols
                               and s not in current_positions]
            
            if other_recommended and remaining > 0:
                selected_others = random.sample(other_recommended, min(remaining, len(other_recommended)))
                return available_defaults + selected_others
            else:
                return available_defaults

def analyze_spot_performance(exchange, symbols):
    """
    分析现货品种的表现，选择盈利潜力最大的品种
    
    参数:
    - exchange: 交易所对象
    - symbols: 待分析的现货品种列表
    
    返回:
    - 按盈利潜力排序的品种列表
    """
    performance_data = []
    
    for symbol in symbols:
        try:
            # 获取历史K线数据
            ohlcv = exchange.fetch_ohlcv(symbol, '1h', limit=72)  # 获取72小时数据
            if len(ohlcv) < 24:
                logging.warning(f"{symbol} 历史数据不足，跳过分析")
                continue
                
            # 转换为DataFrame
            df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            
            # 计算基本指标
            # 1. 24小时价格变化率
            price_change_24h = (df['close'].iloc[-1] / df['close'].iloc[-24] - 1) * 100
            
            # 2. 24小时成交量变化率
            volume_change_24h = (df['volume'].iloc[-24:].mean() / df['volume'].iloc[-48:-24].mean() - 1) * 100
            
            # 3. 波动率 (24小时标准差/均价)
            volatility = df['close'].iloc[-24:].std() / df['close'].iloc[-24:].mean() * 100
            
            # 4. 趋势强度 (当前价格与24小时移动平均线的关系)
            ma24 = df['close'].iloc[-24:].mean()
            trend_strength = (df['close'].iloc[-1] / ma24 - 1) * 100
            
            # 5. 获取当前价格
            current_price = df['close'].iloc[-1]
            
            # 计算综合得分 (盈利潜力)
            # 正向因素: 价格上涨、成交量增加、适度波动、强上升趋势
            score = (
                price_change_24h * 0.4 +  # 价格变化权重40%
                volume_change_24h * 0.2 +  # 成交量变化权重20%
                (10 - abs(volatility - 5)) * 0.1 +  # 适度波动权重10% (理想波动率5%左右)
                trend_strength * 0.3  # 趋势强度权重30%
            )
            
            # 记录分析结果
            performance_data.append({
                'symbol': symbol,
                'price': current_price,
                'price_change_24h': price_change_24h,
                'volume_change_24h': volume_change_24h,
                'volatility': volatility,
                'trend_strength': trend_strength,
                'score': score
            })
            
            logging.info(f"现货分析 {symbol}: 价格={current_price}, 24h变化={price_change_24h:.2f}%, "
                         f"成交量变化={volume_change_24h:.2f}%, 波动率={volatility:.2f}%, "
                         f"趋势强度={trend_strength:.2f}%, 得分={score:.2f}")
                
        except Exception as e:
            logging.error(f"分析 {symbol} 表现失败: {str(e)}")
    
    # 按得分排序 (从高到低)
    performance_data.sort(key=lambda x: x['score'], reverse=True)
    
    # 返回排序后的品种列表
    return [item['symbol'] for item in performance_data]

def select_best_spot_symbols(exchange, count=3):
    """
    选择表现最好的现货品种
    
    参数:
    - exchange: 交易所对象
    - count: 需要选择的品种数量
    
    返回:
    - 选择的品种列表
    """
    try:
        # 首先获取候选品种
        candidates = SPOT_SYMBOLS[:10]  # 从前10个主流币种中选择
        
        # 分析这些品种的表现
        ranked_symbols = analyze_spot_performance(exchange, candidates)
        
        # 选择表现最好的前count个
        selected = ranked_symbols[:count]
        
        if selected:
            logging.info(f"选择表现最好的{len(selected)}个现货品种: {selected}")
            return selected
        else:
            # 如果分析失败，回退到随机选择
            logging.warning("分析现货品种表现失败，回退到随机选择")
            return random.sample(candidates, min(count, len(candidates)))
            
    except Exception as e:
        logging.error(f"选择最佳现货品种失败: {str(e)}")
        # 出错时返回默认选择
        default_symbols = ["BTC/USDT", "ETH/USDT", "SOL/USDT"]
        return default_symbols[:count]
