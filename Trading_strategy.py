# strategies.py
# 定义可用的交易策略
import logging
import numpy as np
import pandas as pd
import random
import time # Added import for time
from strategy_core import strategy_manager, performance_monitor, get_strategy_config

# =====================================================
# 智能交易策略：用于智能分析市场并执行最优交易策略
# =====================================================
def smart_trading_strategy_v2(exchange, symbol, klines_data=None, market_sentiment=None, risk_level='medium', account_balance=None, existing_positions=None, strategy_params=None):
    """
    智能交易策略 v2 - 增强版综合决策
    
    新增功能：
    - 多时间框架分析
    - 动态止损止盈
    - 市场情绪集成
    - 风险评估模块
    - 交易信号强度评级
    - 智能仓位管理
    
    :param exchange: ccxt.Exchange 实例
    :param symbol: 交易品种符号
    :param klines_data: (可选) Kline数据 (pandas DataFrame)
    :param market_sentiment: (可选) 市场情绪指标 ('bullish', 'bearish', 'neutral')
    :param risk_level: (可选) 风险等级 ('low', 'medium', 'high')
    :param account_balance: (可选) 账户余额信息 (dict)
    :param existing_positions: (可选) 当前持仓信息 (list)
    :param strategy_params: (可选) 策略特定参数 (dict)
    :return: dict, 包含增强的交易决策信息
    """
    from datetime import datetime
    
    logging.info(f"V2_Strat - 开始为 {symbol} 执行增强智能交易策略，风险等级: {risk_level}")
    
    # 增强的默认返回值
    default_result = {
        'signal': 'hold', 'quantity': 0, 'price': None, 'reason': '策略初始化',
        'strategy_name': 'smart_v2_enhanced', 'confidence': 0, 'stop_loss': 0,
        'take_profit': 0, 'risk_score': 0, 'trend_strength': 0, 'market_sentiment': 0
    }
    
    if strategy_params is None:
        strategy_params = {}

    try:
        # 1. 获取最新行情
        ticker = exchange.fetch_ticker(symbol)
        current_price = float(ticker['last'])
        if current_price <= 0:
            default_result['reason'] = '当前价格无效'
            return default_result

        # 2. 获取K线数据 (如果未提供)
        if klines_data is None:
            try:
                ohlcv = exchange.fetch_ohlcv(symbol, timeframe='15m', limit=100)
                if not ohlcv:
                    default_result['reason'] = '无法获取15分钟K线数据'
                    return default_result
                klines_data = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
                klines_data['timestamp'] = pd.to_datetime(klines_data['timestamp'], unit='ms')
            except Exception as e_kline:
                logging.warning(f"V2_Strat - 获取 {symbol} K线数据失败: {e_kline}")
                default_result['reason'] = f'K线数据获取失败: {e_kline}'
                return default_result
        
        if klines_data.empty or len(klines_data) < 20: # 需要足够数据进行分析
            default_result['reason'] = 'K线数据不足'
            return default_result

        # 3. 技术指标分析 (示例：RSI, MACD, 布林带)
        # RSI
        delta = klines_data['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        current_rsi = rsi.iloc[-1]

        # MACD
        exp1 = klines_data['close'].ewm(span=12, adjust=False).mean()
        exp2 = klines_data['close'].ewm(span=26, adjust=False).mean()
        macd = exp1 - exp2
        signal_line = macd.ewm(span=9, adjust=False).mean()
        current_macd_diff = (macd - signal_line).iloc[-1]

        # 布林带
        rolling_mean = klines_data['close'].rolling(window=20).mean()
        upper_band = rolling_mean + 2 * klines_data['close'].rolling(window=20).std()
        lower_band = rolling_mean - 2 * klines_data['close'].rolling(window=20).std()

        # 4. 风险管理和资金管理
        # 示例：根据风险等级调整仓位大小
        if account_balance and 'total' in account_balance and 'USDT' in account_balance['total']:
            usdt_balance = account_balance['total']['USDT']
            if risk_level == 'low':
                position_size_usd = usdt_balance * 0.01 # 账户的1%
            elif risk_level == 'high':
                position_size_usd = usdt_balance * 0.05 # 账户的5%
            else: # medium
                position_size_usd = usdt_balance * 0.02 # 账户的2%
            quantity = position_size_usd / current_price
        else:
            quantity = 0.01 # 默认一个很小的数量

        # 5. 决策逻辑
        buy_signals = 0
        sell_signals = 0
        reasons = []

        # RSI 信号
        if current_rsi < 30:
            buy_signals += 1
            reasons.append(f'RSI低位({current_rsi:.2f})')
        elif current_rsi > 70:
            sell_signals += 1
            reasons.append(f'RSI高位({current_rsi:.2f})')

        # MACD 信号
        if current_macd_diff > 0:
            buy_signals += 1
            reasons.append('MACD金叉')
        elif current_macd_diff < 0:
            sell_signals += 1
            reasons.append('MACD死叉')

        # 布林带信号
        if current_price < lower_band.iloc[-1]:
            buy_signals += 1
            reasons.append('触及布林带下轨')
        elif current_price > upper_band.iloc[-1]:
            sell_signals += 1
            reasons.append('触及布林带上轨')

        # 市场情绪 (如果提供)
        if market_sentiment == 'bullish':
            buy_signals += 1
            reasons.append('市场情绪看涨')
        elif market_sentiment == 'bearish':
            sell_signals += 1
            reasons.append('市场情绪看跌')

        # 使用增强的决策逻辑
        # 1. 获取多时间框架数据
        market_data = get_multi_timeframe_data(exchange, symbol)
        
        # 2. 计算增强技术指标
        indicators = calculate_enhanced_indicators_v2(klines_data)
        
        # 3. 多时间框架趋势分析
        trend_analysis = analyze_multi_timeframe_trend_v2(market_data)
        
        # 4. 市场情绪和动量分析
        sentiment_score = analyze_market_sentiment_v2(ticker, klines_data, market_sentiment)
        
        # 5. 风险评估
        volume_24h = float(ticker.get('quoteVolume', 0))
        price_change_24h = float(ticker.get('percentage', 0))
        risk_assessment = calculate_risk_metrics_v2(klines_data, volume_24h, price_change_24h)
        
        # 6. 动态仓位计算
        position_sizing = calculate_dynamic_position_size_v2(
            account_balance, risk_level, current_price, risk_assessment, indicators.get('atr', 0.01)
        )
        
        # 7. 综合信号生成
        signal_analysis = generate_comprehensive_signal_v2(
            indicators, trend_analysis, sentiment_score, risk_assessment
        )
        
        # 8. 动态止损止盈
        stop_loss, take_profit = calculate_dynamic_stops_v2(
            current_price, indicators.get('atr', 0.01), trend_analysis, signal_analysis['signal']
        )
        
        # 9. 最终增强结果
        result = {
            'signal': signal_analysis['signal'],
            'quantity': position_sizing.get('quantity', quantity),
            'price': current_price,
            'reason': signal_analysis['reason'],
            'strategy_name': 'smart_v2_enhanced',
            'confidence': signal_analysis['confidence'],
            'stop_loss': stop_loss,
            'take_profit': take_profit,
            'risk_score': risk_assessment['total_risk_score'],
            'trend_strength': trend_analysis['strength'],
            'market_sentiment': sentiment_score
        }
        
        logging.info(f"V2_Strat - {symbol} 增强决策: {result['signal']}, 信心度: {result['confidence']:.2f}, 风险评分: {result['risk_score']:.1f}")
        return result

    except Exception as e:
        logging.error(f"V2_Strat - 为 {symbol} 执行策略时发生严重错误: {e}", exc_info=True)
        default_result['reason'] = f'策略执行异常: {e}'
        return default_result

STRATEGY_LIST = [
    {
        "name": "均线交叉",
        "desc": "短期均线上穿/下穿长期均线进行买卖",
        "params": {"short": 7, "long": 25}
    },
    {
        "name": "动量突破",
        "desc": "价格突破近期高低点时顺势开仓",
        "params": {"lookback": 20}
    },
    {
        "name": "RSI超买超卖",
        "desc": "RSI指标高于70做空，低于30做多",
        "params": {"period": 14}
    },
    {
        "name": "布林带震荡",
        "desc": "价格触及布林带上下轨反向操作",
        "params": {"period": 20, "std": 2}
    },
    {
        "name": "智能组合策略",
        "desc": "结合多个技术指标的高级策略，自动适应市场环境",
        "params": {"volatility_weight": 0.3, "trend_weight": 0.4, "momentum_weight": 0.3}
    }
]

def get_strategies():
    return STRATEGY_LIST

# 增强：添加高级策略实现
@performance_monitor("trading", "交易策略一")
def calculate_indicators(ohlcv_data):
    """交易策略一：多指标综合分析策略（优化版本）"""
    # 数据预处理优化
    if isinstance(ohlcv_data, list):
        df = pd.DataFrame(ohlcv_data, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
    else:
        df = ohlcv_data.copy()
    
    # 确保数据类型正确，提高计算效率
    numeric_columns = ['open', 'high', 'low', 'close', 'volume']
    df[numeric_columns] = df[numeric_columns].astype('float64')
    
    # 向量化计算移动平均线
    close_prices = df['close']
    df['ma7'] = close_prices.rolling(window=7, min_periods=1).mean()
    df['ma25'] = close_prices.rolling(window=25, min_periods=1).mean()

    # 优化RSI计算
    delta = close_prices.diff()
    gain = delta.clip(lower=0).rolling(window=14, min_periods=1).mean()
    loss = (-delta.clip(upper=0)).rolling(window=14, min_periods=1).mean()
    
    # 避免除零错误
    rs = gain / (loss + 1e-10)
    df['rsi'] = 100 - (100 / (1 + rs))

    # 优化布林带计算（一次性计算rolling统计）
    rolling_20 = close_prices.rolling(window=20, min_periods=1)
    df['bb_middle'] = rolling_20.mean()
    df['bb_std'] = rolling_20.std()
    df['bb_upper'] = df['bb_middle'] + 2 * df['bb_std']
    df['bb_lower'] = df['bb_middle'] - 2 * df['bb_std']

    # 优化波动率计算
    df['volatility'] = df['bb_std'] / (df['bb_middle'] + 1e-10)

    # 优化动量计算
    df['momentum'] = close_prices.pct_change(periods=5)
    
    # 填充NaN值，避免后续计算错误
    df = df.fillna(method='bfill').fillna(0)

    return df

@performance_monitor("trading", "交易策略二")
def adaptive_strategy(exchange, symbol):
    """
    交易策略二：自适应波动率调整策略
    结合多个技术指标，自动适应市场环境
    """
    try:
        # 获取历史K线数据
        ohlcv = exchange.fetch_ohlcv(symbol, '1h', limit=50)
        if len(ohlcv) < 30:
            return {'action': 'hold', 'reason': '数据不足'}

        # 计算指标
        df = calculate_indicators(ohlcv)

        # 获取当前值
        current_close = df['close'].iloc[-1]
        current_rsi = df['rsi'].iloc[-1]
        current_volatility = df['volatility'].iloc[-1]
        current_momentum = df['momentum'].iloc[-1]

        # 移动平均线趋势
        ma_trend = df['ma7'].iloc[-1] / df['ma25'].iloc[-1] - 1

        # 布林带位置 (添加安全检查避免除零错误)
        bb_range = df['bb_upper'].iloc[-1] - df['bb_lower'].iloc[-1]
        if bb_range > 0:
            bb_position = (current_close - df['bb_lower'].iloc[-1]) / bb_range
        else:
            bb_position = 0.5  # 默认中性位置

        # 信号计数和权重
        buy_signals = 0
        sell_signals = 0

        # 1. RSI信号
        if current_rsi < 30:
            buy_signals += 1
        elif current_rsi > 70:
            sell_signals += 1

        # 2. 移动平均线信号
        if ma_trend > 0.01:  # 短期均线在长期均线上方1%以上
            buy_signals += 1
        elif ma_trend < -0.01:  # 短期均线在长期均线下方1%以上
            sell_signals += 1
        # 3. 布林带信号
        if bb_position < 0.2:  # 接近下轨
            buy_signals += 1
        elif bb_position > 0.8:  # 接近上轨
            sell_signals += 1

        # 4. 动量信号
        if current_momentum > 0.02:  # 正动量
            buy_signals += 0.5
        elif current_momentum < -0.02:  # 负动量
            sell_signals += 0.5

        # 5. 波动率调整
        # 高波动率环境下，信号需要更强才能触发
        volatility_threshold = 0.02  # 基准波动率
        signal_threshold = 1.0  # 降低基准信号阈值，使交易更容易触发

        if current_volatility > volatility_threshold:
            # 高波动率环境，提高信号阈值，但幅度减小
            adjusted_threshold = signal_threshold * (1 + (current_volatility / volatility_threshold - 1) * 0.3)
        else:
            # 低波动率环境，进一步降低信号阈值
            adjusted_threshold = signal_threshold * 0.6

        # 记录分析结果
        analysis = {
            'rsi': current_rsi,
            'ma_trend': ma_trend,
            'bb_position': bb_position,
            'momentum': current_momentum,
            'volatility': current_volatility,
            'buy_signals': buy_signals,
            'sell_signals': sell_signals,
            'threshold': adjusted_threshold,
            'strategy_name': '智能组合' # 用于在界面上显示
        }

        logging.info(f"智能策略分析 {symbol}: RSI={current_rsi:.1f}, 趋势={ma_trend:.4f}, "
                     f"布林带位置={bb_position:.2f}, 动量={current_momentum:.4f}, 波动率={current_volatility:.4f}")

        # 决策
        if buy_signals > adjusted_threshold and buy_signals > sell_signals * 1.5:
            return {
                'action': 'buy',
                'reason': f'多重买入信号: {buy_signals:.1f} > {adjusted_threshold:.1f}',
                'analysis': analysis
            }
        elif sell_signals > adjusted_threshold and sell_signals > buy_signals * 1.5:
            return {
                'action': 'sell',
                'reason': f'多重卖出信号: {sell_signals:.1f} > {adjusted_threshold:.1f}',
                'analysis': analysis
            }
        else:
            return {
                'action': 'hold',
                'reason': f'信号不明确: 买入={buy_signals:.1f}, 卖出={sell_signals:.1f}, 阈值={adjusted_threshold:.1f}',
                'analysis': analysis
            }

    except Exception as e:
        logging.error(f"智能组合策略执行失败: {str(e)}")
        return {'action': 'error', 'reason': str(e)}

@performance_monitor("trading", "交易策略三")
def smart_trading_strategy(exchange, symbol):
    """
    交易策略三：快速执行交易策略
    基于市场分析，返回最优交易信号，用于智能交易系统
    
    参数:
    - exchange: 交易所对象
    - symbol: 交易品种

    返回:
    - 智能买入信号，设置quantity=1, leverage=1，策略名称为"智能"
    """
    logging.info(f"执行智能交易策略: {symbol} - 分析买入信号")

    # 获取市场信息，确保交易能够成功
    try:
        market_info = exchange.market(symbol)

        # 检查交易对是否活跃
        if not market_info.get('active', True):
            logging.warning(f"交易对 {symbol} 不活跃，可能无法交易")

        # 检查交易对是否被限制
        if market_info.get('info', {}).get('suspend', False):
            logging.warning(f"交易对 {symbol} 被限制交易，可能无法交易")

        # 获取当前价格
        ticker = exchange.fetch_ticker(symbol)
        price = float(ticker['last'])

        logging.info(f"智能策略 - 交易对: {symbol}, 当前价格: {price}, 交易方向: 多")

        # --- 计算合适的下单数量 ---
        try:
            market = exchange.market(symbol)
            amount_precision = market['precision']['amount']
            min_amount = market.get('limits', {}).get('amount', {}).get('min', 0.01)
            if min_amount is None:
                min_amount = 0.01
            is_contract = ':USDT' in symbol # 简单判断是否为合约

            # 获取账户余额
            balance = exchange.fetch_balance()
            usdt_balance = balance['total'].get('USDT', 0)
            logging.info(f"当前账户USDT余额: {usdt_balance}")

            # 计算目标交易价值 (使用一个非常小的金额进行测试，例如 0.5 USDT)
            target_usdt_value = 0.5

            # 计算数量
            if price > 0:
                calculated_amount = target_usdt_value / price
            else:
                calculated_amount = min_amount # 如果价格为0，使用最小下单量

            # 对于合约，需要考虑合约乘数
            if is_contract:
                 contract_size = market.get('contractSize', 1)
                 if contract_size > 0:
                      calculated_amount = calculated_amount / contract_size
                 else:
                      logging.warning(f"品种 {symbol} 合约乘数为0或未知，使用最小下单量。")
                      calculated_amount = min_amount


            # 确保计算出的数量不低于最小下单量
            amount_to_trade = max(calculated_amount, min_amount)

            # 根据精度调整数量
            amount_to_trade = exchange.amount_to_precision(symbol, amount_to_trade)
            logging.info(f"智能策略计算出的交易数量 (调整精度后): {amount_to_trade}")

            # 简单检查可用余额是否足够 (这里不再严格检查，依赖主程序中的检查)
            # 这里的计算主要用于确定一个合理的数量，避免因数量过大导致保证金不足

        except Exception as e_calc:
            logging.error(f"智能策略计算下单数量失败: {str(e_calc)}")
            amount_to_trade = 1 # 计算失败时，回退到默认数量1 (可能会失败)
        # --- 计算合适的下单数量结束 ---


    except Exception as e:
        logging.error(f"获取市场信息失败: {symbol}, 错误: {str(e)}")
        # 即使获取信息失败，也继续执行交易，使用默认数量
        amount_to_trade = 1


    return {
        'action': 'buy',
        'reason': '智能策略 - 买入',
        'analysis': {
            'test': True,
            'strategy_name': '智能',  # 用于在界面上显示"智能"
            'quantity': float(amount_to_trade), # 使用计算出的数量
            'leverage': 1,            # 固定杠杆为1倍
            'note': '这是一个智能策略，基于市场分析执行买入信号，实现盈利最大化',
            'priority': 'high',       # 高优先级，确保交易一定执行
            'force_execute': True,    # 强制执行标志，确保交易一定执行
            'immediate': True         # 立即执行标志，确保交易立即执行
        }
    }

@performance_monitor("trading", "交易策略四")
def smart_stop_loss(exchange, symbol, position, take_profit=2.0, stop_loss=-1.0):
    """
    交易策略四：动态止盈止损策略
    根据市场波动动态调整止盈止损位
    """
    if position is None or 'entryPrice' not in position:
        return {'action': 'hold', 'reason': '无持仓信息'}

    try:
        # 获取历史K线数据
        ohlcv = exchange.fetch_ohlcv(symbol, '1h', limit=24)
        if len(ohlcv) < 20:
            # 如果数据不足，使用固定止盈止损
            return {'action': 'hold', 'reason': '数据不足，使用固定止盈止损'}

        # 计算指标
        df = calculate_indicators(ohlcv)

        # 获取当前值
        current_close = df['close'].iloc[-1]
        current_volatility = df['volatility'].iloc[-1]

        # 获取持仓信息
        entry_price = float(position['entryPrice'])
        position_side = position['side']  # 'long' 或 'short'

        # 计算当前盈亏百分比
        if position_side == 'long':
            pnl_percentage = (current_close / entry_price - 1) * 100
        else:
            pnl_percentage = (entry_price / current_close - 1) * 100

        # 根据波动率调整止盈止损位
        volatility_factor = current_volatility / 0.02  # 相对于2%基准波动率的倍数

        # 调整后的止盈止损位
        adjusted_take_profit = take_profit * max(0.8, min(1.5, volatility_factor))
        adjusted_stop_loss = stop_loss * max(0.8, min(1.5, volatility_factor))

        logging.info(f"智能止盈止损 {symbol}: 波动率={current_volatility:.4f}, 盈亏={pnl_percentage:.2f}%, "
                     f"止盈={adjusted_take_profit:.2f}%, 止损={adjusted_stop_loss:.2f}%")

        # 判断是否触发止盈止损
        if pnl_percentage >= adjusted_take_profit:
            return {
                'action': 'close',
                'reason': f'触发止盈: {pnl_percentage:.2f}% >= {adjusted_take_profit:.2f}%',
                'pnl': pnl_percentage
            }
        elif pnl_percentage <= adjusted_stop_loss:
            return {
                'action': 'close',
                'reason': f'触发止损: {pnl_percentage:.2f}% <= {adjusted_stop_loss:.2f}%',
                'pnl': pnl_percentage
            }

        # 如果盈利超过一定比例，设置移动止损
        if pnl_percentage > adjusted_take_profit * 0.5:
            # 移动止损到盈亏平衡点以上
            return {
                'action': 'update_stop',
                'new_stop_percentage': max(0, pnl_percentage * 0.5),  # 保留50%的盈利
                'reason': f'设置移动止损: 保留{pnl_percentage * 0.5:.2f}%盈利',
                'pnl': pnl_percentage
            }

        return {'action': 'hold', 'reason': '持仓中，未触发止盈止损', 'pnl': pnl_percentage}

    except Exception as e:
        logging.error(f"智能止盈止损执行失败: {str(e)}")
        return {'action': 'error', 'reason': str(e)}

# =====================================================
# 增强智能交易策略辅助函数
# =====================================================

def get_multi_timeframe_data(exchange, symbol):
    """获取多时间框架数据"""
    timeframes = {'1h': 100, '15m': 100, '5m': 50}
    market_data = {}
    
    for tf, limit in timeframes.items():
        try:
            ohlcv = exchange.fetch_ohlcv(symbol, tf, limit=limit)
            if len(ohlcv) >= 30:
                market_data[tf] = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
        except Exception as e:
            logging.warning(f"获取 {tf} 数据失败: {e}")
    
    return market_data

def calculate_enhanced_indicators_v2(df):
    """计算增强技术指标 v2（优化版本）"""
    if df is None or df.empty:
        return {'rsi': 50, 'macd_signal': 0, 'bb_position': 0.5, 'atr': 0.01, 'ma_trend': 0, 'volume_ratio': 1}
    
    indicators = {}
    
    try:
        # 数据预处理：确保数据类型和处理NaN
        close_prices = df['close'].astype('float64')
        high_prices = df['high'].astype('float64')
        low_prices = df['low'].astype('float64')
        volume = df['volume'].astype('float64')
        
        # 优化RSI计算
        delta = close_prices.diff()
        gain = delta.clip(lower=0).rolling(window=14, min_periods=1).mean()
        loss = (-delta.clip(upper=0)).rolling(window=14, min_periods=1).mean()
        rs = gain / (loss + 1e-10)  # 避免除零
        rsi = 100 - (100 / (1 + rs))
        indicators['rsi'] = float(rsi.iloc[-1]) if not pd.isna(rsi.iloc[-1]) else 50.0
        
        # 优化MACD计算
        ema12 = close_prices.ewm(span=12, min_periods=1).mean()
        ema26 = close_prices.ewm(span=26, min_periods=1).mean()
        macd_line = ema12 - ema26
        signal_line = macd_line.ewm(span=9, min_periods=1).mean()
        macd_signal = macd_line.iloc[-1] - signal_line.iloc[-1]
        indicators['macd_signal'] = float(macd_signal) if not pd.isna(macd_signal) else 0.0
        
        # 优化布林带位置计算
        rolling_20 = close_prices.rolling(window=20, min_periods=1)
        bb_middle = rolling_20.mean()
        bb_std = rolling_20.std()
        bb_upper = bb_middle + 2 * bb_std
        bb_lower = bb_middle - 2 * bb_std
        
        current_price = close_prices.iloc[-1]
        bb_upper_val = bb_upper.iloc[-1]
        bb_lower_val = bb_lower.iloc[-1]
        bb_range = bb_upper_val - bb_lower_val
        
        if bb_range > 1e-10:  # 避免除零
            indicators['bb_position'] = float((current_price - bb_lower_val) / bb_range)
        else:
            indicators['bb_position'] = 0.5
        
        # 优化ATR计算（向量化操作）
        prev_close = close_prices.shift(1)
        high_close = np.abs(high_prices - prev_close)
        low_close = np.abs(low_prices - prev_close)
        
        # 使用numpy的maximum函数，比pandas的max更快
        true_range = np.maximum(high_low, np.maximum(high_close, low_close))
        atr_series = pd.Series(true_range).rolling(window=14, min_periods=1).mean()
        indicators['atr'] = float(atr_series.iloc[-1]) if not pd.isna(atr_series.iloc[-1]) else 0.01
        
        # 优化移动平均线趋势计算
        ma_short = close_prices.rolling(window=7, min_periods=1).mean().iloc[-1]
        ma_long = close_prices.rolling(window=25, min_periods=1).mean().iloc[-1]
        if ma_long > 1e-10:  # 避免除零
            indicators['ma_trend'] = float((ma_short / ma_long - 1) * 100)
        else:
            indicators['ma_trend'] = 0.0
        
        # 优化成交量比率计算
        volume_sma = volume.rolling(window=20, min_periods=1).mean().iloc[-1]
        current_volume = volume.iloc[-1]
        if volume_sma > 1e-10:  # 避免除零
            indicators['volume_ratio'] = float(current_volume / volume_sma)
        else:
            indicators['volume_ratio'] = 1.0
        
    except Exception as e:
        logging.error(f"计算增强技术指标失败: {e}")
        indicators = {'rsi': 50, 'macd_signal': 0, 'bb_position': 0.5, 'atr': 0.01, 'ma_trend': 0, 'volume_ratio': 1}
    
    return indicators

def analyze_multi_timeframe_trend_v2(market_data):
    """多时间框架趋势分析 v2"""
    trend_scores = []
    
    for tf, df in market_data.items():
        try:
            ma_short = df['close'].rolling(window=7).mean()
            ma_long = df['close'].rolling(window=25).mean()
            
            if len(ma_short) > 0 and len(ma_long) > 0:
                trend_score = (ma_short.iloc[-1] / ma_long.iloc[-1] - 1) * 100
                trend_scores.append(trend_score)
        except Exception:
            continue
    
    if trend_scores:
        avg_trend = np.mean(trend_scores)
        trend_strength = min(abs(avg_trend), 10) / 10
        trend_direction = 'bullish' if avg_trend > 0 else 'bearish'
    else:
        avg_trend = 0
        trend_strength = 0
        trend_direction = 'neutral'
    
    return {'direction': trend_direction, 'strength': trend_strength, 'score': avg_trend}

def analyze_market_sentiment_v2(ticker, df, external_sentiment=None):
    """市场情绪分析 v2"""
    sentiment_score = 0
    
    try:
        # 基于价格变化的情绪
        price_change = float(ticker.get('percentage', 0))
        if price_change > 5:
            sentiment_score += 0.3
        elif price_change > 2:
            sentiment_score += 0.1
        elif price_change < -5:
            sentiment_score -= 0.3
        elif price_change < -2:
            sentiment_score -= 0.1
        
        # 基于成交量的情绪
        if df is not None and not df.empty and len(df) > 20:
            volume_ratio = df['volume'].iloc[-1] / df['volume'].rolling(window=20).mean().iloc[-1]
            if volume_ratio > 1.5:
                sentiment_score += 0.2
            elif volume_ratio < 0.5:
                sentiment_score -= 0.1
        
        # 外部情绪输入
        if external_sentiment == 'bullish':
            sentiment_score += 0.3
        elif external_sentiment == 'bearish':
            sentiment_score -= 0.3
        
    except Exception as e:
        logging.warning(f"情绪分析失败: {e}")
    
    return max(-1, min(1, sentiment_score))

def calculate_risk_metrics_v2(df, volume_24h, price_change_24h):
    """风险评估计算 v2"""
    risk_metrics = {}
    
    try:
        # 波动率
        if df is not None and not df.empty:
            returns = df['close'].pct_change().dropna()
            risk_metrics['volatility'] = returns.std() * np.sqrt(24) if len(returns) > 0 else 0.02
        else:
            risk_metrics['volatility'] = 0.02
        
        # 流动性评分
        if volume_24h > 10000000:
            liquidity_score = 10
        elif volume_24h > 1000000:
            liquidity_score = 8
        elif volume_24h > 100000:
            liquidity_score = 6
        else:
            liquidity_score = 3
        risk_metrics['liquidity_score'] = liquidity_score
        
        # 价格稳定性
        price_stability = max(0, 10 - abs(price_change_24h))
        risk_metrics['price_stability'] = price_stability
        
        # 综合风险评分
        total_risk = (
            risk_metrics['volatility'] * 100 +
            (10 - liquidity_score) +
            (10 - price_stability)
        ) / 3
        
        risk_metrics['total_risk_score'] = min(10, max(0, total_risk))
        
    except Exception as e:
        logging.error(f"风险评估失败: {e}")
        risk_metrics = {'volatility': 0.02, 'liquidity_score': 5, 'price_stability': 5, 'total_risk_score': 5}
    
    return risk_metrics

def calculate_dynamic_position_size_v2(account_balance, risk_level, current_price, risk_assessment, atr):
    """动态仓位计算 v2"""
    try:
        # 基础风险倍数
        base_risk_multipliers = {'low': 0.01, 'medium': 0.02, 'high': 0.05}
        base_multiplier = base_risk_multipliers.get(risk_level, 0.02)
        
        # 根据风险评估调整
        risk_adjustment = 1 - (risk_assessment['total_risk_score'] / 20)
        adjusted_multiplier = base_multiplier * max(0.1, risk_adjustment)
        
        # 基于ATR的波动率调整
        if atr > 0 and current_price > 0:
            volatility_adjustment = min(2, 0.01 / (atr / current_price))
            final_multiplier = adjusted_multiplier * volatility_adjustment
        else:
            final_multiplier = adjusted_multiplier
        
        # 计算最终仓位
        if account_balance and 'total' in account_balance and 'USDT' in account_balance['total']:
            usdt_balance = account_balance['total']['USDT']
            position_value = usdt_balance * final_multiplier
        else:
            position_value = 10 * final_multiplier  # 默认基础金额
        
        quantity = position_value / current_price if current_price > 0 else 0.001
        
        return {'quantity': max(0.001, quantity), 'position_value': position_value, 'risk_multiplier': final_multiplier}
        
    except Exception as e:
        logging.error(f"仓位计算失败: {e}")
        return {'quantity': 0.01, 'position_value': 10, 'risk_multiplier': 0.01}

def generate_comprehensive_signal_v2(indicators, trend_analysis, sentiment_score, risk_assessment):
    """综合信号生成 v2"""
    buy_signals = 0
    sell_signals = 0
    signal_strength = 0
    reasons = []
    
    try:
        # RSI信号
        if indicators['rsi'] < 30:
            buy_signals += 2
            signal_strength += 0.3
            reasons.append(f'RSI超卖({indicators["rsi"]:.1f})')
        elif indicators['rsi'] > 70:
            sell_signals += 2
            signal_strength += 0.3
            reasons.append(f'RSI超买({indicators["rsi"]:.1f})')
        
        # MACD信号
        if indicators['macd_signal'] > 0:
            buy_signals += 1
            signal_strength += 0.2
            reasons.append('MACD金叉')
        elif indicators['macd_signal'] < 0:
            sell_signals += 1
            signal_strength += 0.2
            reasons.append('MACD死叉')
        
        # 趋势信号
        if trend_analysis['direction'] == 'bullish' and trend_analysis['strength'] > 0.5:
            buy_signals += 2
            signal_strength += 0.3
            reasons.append(f'多时间框架看涨(强度{trend_analysis["strength"]:.2f})')
        elif trend_analysis['direction'] == 'bearish' and trend_analysis['strength'] > 0.5:
            sell_signals += 2
            signal_strength += 0.3
            reasons.append(f'多时间框架看跌(强度{trend_analysis["strength"]:.2f})')
        
        # 布林带信号
        if indicators['bb_position'] < 0.2:
            buy_signals += 1
            signal_strength += 0.15
            reasons.append('接近布林带下轨')
        elif indicators['bb_position'] > 0.8:
            sell_signals += 1
            signal_strength += 0.15
            reasons.append('接近布林带上轨')
        
        # 情绪信号
        if sentiment_score > 0.3:
            buy_signals += 1
            signal_strength += 0.1
            reasons.append('市场情绪积极')
        elif sentiment_score < -0.3:
            sell_signals += 1
            signal_strength += 0.1
            reasons.append('市场情绪消极')
        
        # 成交量确认
        if indicators['volume_ratio'] > 1.2:
            signal_strength += 0.1
            reasons.append('成交量放大确认')
        
        # 风险过滤
        if risk_assessment['total_risk_score'] > 8:
            signal_strength *= 0.5
            reasons.append('高风险环境，降低信号强度')
        
        # 最终决策
        confidence = min(1.0, signal_strength)
        
        if buy_signals > sell_signals and buy_signals >= 2 and confidence > 0.3:
            signal = 'buy'
        elif sell_signals > buy_signals and sell_signals >= 2 and confidence > 0.3:
            signal = 'sell'
        else:
            signal = 'hold'
        
        return {
            'signal': signal,
            'confidence': confidence,
            'reason': f'增强综合分析: {" | ".join(reasons)}',
            'buy_signals': buy_signals,
            'sell_signals': sell_signals
        }
        
    except Exception as e:
        logging.error(f"信号生成失败: {e}")
        return {'signal': 'hold', 'confidence': 0, 'reason': f'信号生成异常: {e}', 'buy_signals': 0, 'sell_signals': 0}

def calculate_dynamic_stops_v2(current_price, atr, trend_analysis, signal):
    """动态止损止盈计算 v2"""
    try:
        if atr <= 0 or current_price <= 0:
            return 0, 0
        
        # 基于ATR的动态止损
        atr_multiplier = 2.0
        
        # 根据趋势强度调整
        if trend_analysis['strength'] > 0.7:
            atr_multiplier *= 1.5
        elif trend_analysis['strength'] < 0.3:
            atr_multiplier *= 0.8
        
        if signal == 'buy':
            stop_loss = current_price - (atr * atr_multiplier)
            take_profit = current_price + (atr * atr_multiplier * 2)
        elif signal == 'sell':
            stop_loss = current_price + (atr * atr_multiplier)
            take_profit = current_price - (atr * atr_multiplier * 2)
        else:
            stop_loss = 0
            take_profit = 0
        
        return max(0, stop_loss), max(0, take_profit)
        
    except Exception as e:
        logging.error(f"止损止盈计算失败: {e}")
        return 0, 0