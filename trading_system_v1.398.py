import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入策略管理系统
from strategy_core import (
    strategy_core as strategy_manager,
    execute_select_product_strategies,
    execute_trading_strategies,
    execute_trading_strategy,
    get_strategy_performance_report,
    list_available_strategies
)
import traceback
import logging

# 启动日志和全局异常捕获
# 优化日志配置
from logging.handlers import RotatingFileHandler
import os

# 配置日志轮转
log_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s')
log_handler = RotatingFileHandler(
    'trading_system.log',
    maxBytes=50*1024*1024,  # 50MB
    backupCount=10,
    encoding='utf-8'
)
log_handler.setFormatter(log_formatter)

# 控制台输出
console_handler = logging.StreamHandler()
console_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))

# 配置根日志器
logging.basicConfig(
    level=logging.INFO,
    handlers=[log_handler, console_handler]
)
logging.info('程序启动，环境变量 sys.frozen = %s' % getattr(sys, 'frozen', False))

def global_exception_hook(exctype, value, tb):
    err_msg = ''.join(traceback.format_exception(exctype, value, tb))
    logging.error('全局异常捕获：' + err_msg)
    try:
        import tkinter as tk
        from tkinter import messagebox
        # 使用临时窗口显示错误，避免与主程序窗口冲突
        temp_root = tk.Tk()
        temp_root.withdraw()
        messagebox.showerror('程序异常', '程序发生严重错误，已写入trading_system.log：\n' + str(value))
        temp_root.destroy()
    except Exception:
        pass
    sys.exit(1)

sys.excepthook = global_exception_hook

import tkinter as tk
from tkinter import ttk, messagebox
from Select_product import smart_select_symbols_v2
from Trading_strategy import smart_trading_strategy_v2
import ccxt
import json
import threading
import time
import numpy as np
import sys
import requests
import pandas as pd
import os
import asyncio
from concurrent.futures import ThreadPoolExecutor, as_completed
import base64
import logging
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from cryptography.fernet import Fernet, InvalidToken
import pkg_resources
import random
# 已删除测试相关导入

# 日志配置 (已在程序开头统一配置)

# --- 新增的智能选择和交易策略函数定义 ---








# --- 结束新增函数定义 ---

RECOMMENDED_CCXT_VERSION = '4.0.104'  # 可根据实际情况调整

class TradingSystem:
    # 交易状态常量
    STATUS_OPEN = 'open'
    STATUS_CLOSED = 'closed'
    STATUS_CLOSING = 'closing'
    STATUS_CLOSED_BY_SL_TP = 'closed_by_sl_tp'
    STATUS_CLOSED_BY_MANAGEMENT = 'closed_by_management'
    
    # 交易方向常量
    SIDE_LONG = 'long'
    SIDE_SHORT = 'short'
    
    # 交易类型常量
    TYPE_CONTRACT = '合约'
    TYPE_SPOT = '现货'
    
    # 策略相关常量
    STRATEGY_UNKNOWN = 'Unknown/Existing'
    
    def __init__(self):
        # 智能错误处理计数器（必须在最前面初始化，因为其他方法可能会用到）
        self._error_counts = {}
        self._last_error_time = {}
        self._max_error_frequency = 5  # 每分钟最多显示5次同类错误
        
        # 配置缓存优化（必须在load_config之前初始化）
        self._config_cache = None
        self._config_cache_time = 0
        self._config_cache_timeout = 30  # 配置缓存30秒
        
        # 加密密钥必须在load_config之前初始化，因为保存功能需要用到
        self.encryption_key = self.load_encryption_key()
        
        # 加载性能配置
        self._load_performance_config()
        
        self.config = self.load_config()
        
        # 初始化实时监控器
        self.real_time_monitor = None
        self._init_real_time_monitor()
        self.running = False
        self.exchange = None
        self.trade_status_details = {} # 初始化交易状态详情
        
        # 线程管理优化
        self._active_threads = set()  # 跟踪活跃线程
        self._thread_lock = threading.Lock()  # 线程安全锁
        
        # UI更新优化
        self._ui_update_queue = []  # UI更新队列
        self._ui_update_scheduled = False  # 是否已安排UI更新
        self._last_ui_update = 0  # 上次UI更新时间
        self._ui_update_interval = 0.2  # UI更新间隔（200ms，进一步减少频率）
        
        # 数据缓存优化
        self._market_data_cache = {}  # 市场数据缓存
        self._market_data_cache_time = 0
        self._market_data_cache_timeout = 60  # 市场数据缓存60秒
        
        
        # 弹窗状态控制，防止重复弹出
        self._dialog_states = {
            'stop_trading_shown': False,
            'error_dialog_shown': False,
            'warning_dialog_shown': False
        }
        
        # 窗口管理统一化
        self.main_root = None  # 主窗口实例
        self.login_root = None  # 登录窗口实例
    
    def update_status_bar(self, message):
        """更新状态栏信息"""
        if hasattr(self, 'main_status_var') and self.main_status_var:
            self.main_status_var.set(message)
        if hasattr(self, 'main_root') and self.main_root:
            self.main_root.update_idletasks()
    
    def smart_error_handler(self, error_type, error_msg, show_popup=True):
        """
        智能错误处理：避免频繁弹窗，提升用户体验
        """
        current_time = time.time()
        
        # 检查错误频率
        if error_type not in self._error_counts:
            self._error_counts[error_type] = 0
            self._last_error_time[error_type] = current_time
        
        # 重置计数器（每分钟）
        if current_time - self._last_error_time[error_type] > 60:
            self._error_counts[error_type] = 0
            self._last_error_time[error_type] = current_time
        
        self._error_counts[error_type] += 1
        
        # 记录日志
        logging.error(f"[{error_type}] {error_msg}")
        
        # 智能弹窗控制
        if show_popup and self._error_counts[error_type] <= self._max_error_frequency:
            if self._error_counts[error_type] == self._max_error_frequency:
                error_msg += f"\n\n注意：此类错误已达到显示上限，后续同类错误将只记录到日志中。"
            
            try:
                messagebox.showerror(f"错误 - {error_type}", error_msg)
            except Exception:
                pass  # 避免弹窗本身出错
        
        return self._error_counts[error_type] <= self._max_error_frequency
    
    def _register_thread(self, thread):
        """注册线程到活跃线程集合"""
        with self._thread_lock:
            self._active_threads.add(thread)
    
    def _unregister_thread(self, thread):
        """从活跃线程集合中移除线程"""
        with self._thread_lock:
            self._active_threads.discard(thread)
    
    def _cleanup_finished_threads(self):
        """清理已完成的线程"""
        with self._thread_lock:
             finished_threads = {t for t in self._active_threads if not t.is_alive()}
             self._active_threads -= finished_threads
    
    def _queue_ui_update(self, update_func, priority='normal'):
        """将UI更新加入队列，支持优先级和智能合并"""
        # 智能去重：避免重复的表格更新
        func_name = getattr(update_func, '__name__', str(update_func))
        if func_name == 'update_main_trading_table':
            # 移除队列中已有的表格更新，只保留最新的
            self._ui_update_queue = [f for f in self._ui_update_queue 
                                   if getattr(f, '__name__', str(f)) != 'update_main_trading_table']
        
        # 根据优先级插入队列
        if priority == 'high':
            self._ui_update_queue.insert(0, update_func)
        else:
            self._ui_update_queue.append(update_func)
        
        # 限制队列长度，防止积压
        max_queue_size = 10
        if len(self._ui_update_queue) > max_queue_size:
            self._ui_update_queue = self._ui_update_queue[-max_queue_size:]
        
        if not self._ui_update_scheduled:
            self._ui_update_scheduled = True
            # 动态调整延迟：队列越长延迟越短
            base_delay = self._ui_update_interval
            queue_factor = max(0.3, 1 - len(self._ui_update_queue) * 0.1)
            delay = max(0, int((self._last_ui_update + base_delay * queue_factor - time.time()) * 1000))
            if hasattr(self, 'main_root') and self.main_root:
                self.main_root.after(delay, self._process_ui_updates)
    
    def _process_ui_updates(self):
        """批量处理UI更新队列（优化版本）"""
        self._ui_update_scheduled = False
        start_time = time.time()
        self._last_ui_update = start_time
        
        # 批量执行所有UI更新
        updates_to_process = self._ui_update_queue[:]
        self._ui_update_queue.clear()
        
        processed_count = 0
        for update_func in updates_to_process:
            try:
                update_func()
                processed_count += 1
            except Exception as e:
                logging.error(f"UI更新失败: {str(e)[:100]}")
        
        # 性能监控：记录处理时间
        process_time = time.time() - start_time
        if process_time > 0.1:  # 超过100ms记录警告
            logging.warning(f"UI更新耗时过长: {process_time:.3f}s, 处理{processed_count}个更新")
        
        # 更新性能统计
        if not hasattr(self, '_ui_process_stats'):
            self._ui_process_stats = {'count': 0, 'total_time': 0}
        self._ui_process_stats['count'] += 1
        self._ui_process_stats['total_time'] += process_time
        
        # 每100次更新记录平均性能
        if self._ui_process_stats['count'] % 100 == 0:
            avg_time = self._ui_process_stats['total_time'] / self._ui_process_stats['count']
            logging.info(f"UI更新性能统计: 平均耗时{avg_time:.3f}s, 总计{self._ui_process_stats['count']}次")
            # 重置统计
            self._ui_process_stats = {'count': 0, 'total_time': 0}
    
    
    def _get_cached_markets(self, force_reload=False):
        """获取缓存的市场数据，减少API调用"""
        current_time = time.time()
        
        if (not force_reload and self._market_data_cache and 
            current_time - self._market_data_cache_time < self._market_data_cache_timeout):
            return self._market_data_cache
        
        try:
            if self.exchange:
                markets = self.exchange.load_markets(True)
                self._market_data_cache = markets
                self._market_data_cache_time = current_time
                return markets
        except Exception as e:
            logging.error(f"加载市场数据失败: {str(e)}")
            return self._market_data_cache if self._market_data_cache else {}
        self.trading_pairs = [
            "BTC/USDT:USDT", "ETH/USDT:USDT", "SOL/USDT:USDT", "ADA/USDT:USDT", "DOGE/USDT:USDT",
            "XRP/USDT:USDT", "MATIC/USDT:USDT", "AVAX/USDT:USDT", "LINK/USDT:USDT", "DOT/USDT:USDT"
        ]
        self.positions = {}
        self.total_profit = 0.0
        self.fee_rate = 0.0002  # Maker 手续费 0.02%
        self.current_strategy = "strategy1"  # 默认策略
        self.proxies = None
        self.use_proxy_var = None
        self.domain_var = None
        self.poll_interval = 30
        self.strategy_params = {
            "strategy1": {"rsi_period": 14, "bb_period": 15, "stop_loss": 0.015, "take_profit": 0.01},
            "strategy2": {"rsi_period": 14, "stop_loss": 0.015, "take_profit": 0.01},
            "strategy3": {"bb_period": 15, "stop_loss": 0.015, "take_profit": 0.01},
            "combo_trend_breakout": {"bb_period": 15, "stop_loss": 0.015, "take_profit": 0.01},
            "combo_reversal_mtf": {"rsi_period": 14, "stop_loss": 0.015, "take_profit": 0.01},
            "combo_bollinger_oscillation": {"bb_period": 15, "stop_loss": 0.015, "take_profit": 0.01}
        }
        self._last_balance_fetch_time = 0 # 上次获取余额的时间
        self._cached_total_balance_usdt = 0 # 缓存的账户总余额
        self.trade_status_details = {} # 用于跟踪每个品种的详细交易状态
        self.active_orders = {} # {symbol: {order_id: order_data}}
        self._initial_symbols = [] # 用于标记初始的交易对
        # 修正：无论是否frozen都弹出登录框，方便调试
        if getattr(sys, 'frozen', False):
            self.check_environment()
        else:
            print("开发模式：直接进入登录界面，方便调试！")
        # 登录弹窗逻辑移到main函数中调用，避免在__init__中阻塞


    def show_login_dialog(self):
        import tkinter as tk
        from tkinter import ttk, messagebox
        
        # 确保只有一个登录窗口存在
        if self.login_root is not None:
            try:
                self.login_root.destroy()
            except:
                pass
            self.login_root = None
        
        # 使用现代主题优化GUI外观
        try:
            from ttkthemes import ThemedTk
            self.login_root = ThemedTk(theme="arc")
        except ImportError:
            # 如果没有ttkthemes，使用标准tkinter并应用现代主题
            self.login_root = tk.Tk()
            style = ttk.Style()
            try:
                style.theme_use("clam")  # 使用现代clam主题
            except:
                pass  # 如果主题不可用，使用默认主题
        
        root = self.login_root  # 保持向后兼容
        
        root.title("欧易智能全自动交易系统 V1.398")
        root.geometry("570x440")
        root.resizable(False, False)
        
        # 居中显示窗口
        root.update_idletasks()
        x = (root.winfo_screenwidth() // 2) - (570 // 2)
        y = (root.winfo_screenheight() // 2) - (440 // 2)
        root.geometry(f"570x440+{x}+{y}")
        
        # 设置窗口图标和样式
        try:
            root.iconbitmap(default='')  # 移除默认图标
        except:
            pass

        tabControl = ttk.Notebook(root)
        tab_sim = ttk.Frame(tabControl)
        tab_live = ttk.Frame(tabControl)
        tabControl.add(tab_sim, text='模拟交易')
        tabControl.add(tab_live, text='实盘交易')
        tabControl.pack(expand=1, fill="both")

        labels = ["账号:", "密码:", "API 密钥:", "Secret Key:", "Passphrase:"]
        entries = []
        show_vars = []
        from functools import partial
        for i, label in enumerate(labels):
            ttk.Label(tab_sim, text=label).place(x=20, y=20+35*i)
            is_password = i in [1,3,4]
            entry = ttk.Entry(tab_sim, show='*' if is_password else '')
            entry.place(x=110, y=20+35*i, width=260)
            entries.append(entry)
            show_var = tk.BooleanVar(value=False)
            show_vars.append(show_var)
            def toggle_show(idx, *_):
                entry = entries[idx]
                is_pwd = idx in [1,3,4]
                entry.config(show='' if show_vars[idx].get() else ('*' if is_pwd else ''))
            show_var.trace_add('write', partial(toggle_show, i))
            ttk.Checkbutton(tab_sim, text="显示", variable=show_var).place(x=380, y=20+35*i)

        # 必须先定义所有变量再使用，修复 save_var 异常
        proxy_var = tk.BooleanVar()
        domain_var = tk.StringVar(value="com")
        save_var = tk.BooleanVar()

        ttk.Checkbutton(tab_sim, text="使用代理 (127.0.0.1:10808)", variable=proxy_var).place(x=110, y=200)
        ttk.Label(tab_sim, text="OKX子域名:").place(x=20, y=240)
        ttk.Combobox(tab_sim, textvariable=domain_var, values=["com","eu"]).place(x=110, y=240, width=80)
        ttk.Label(tab_sim, text="(选择 'eu' 如果您在欧盟经济区)").place(x=200, y=240)
        ttk.Checkbutton(tab_sim, text="保存账号和密钥", variable=save_var).place(x=110, y=270)

        # 保存账号和密钥功能
        import json, os
        config_file = "user_config.json"
        
        # 保存对self的引用，以便在嵌套函数中使用
        trading_system_instance = self
        
        def save_account():
            if not save_var.get():  # 只有勾选了保存选项才执行保存
                return
                
            try:
                # 使用当前实例的加密方法
                data = {
                    "username": entries[0].get().strip(), # 用户名通常保持明文
                    "proxy": proxy_var.get(),
                    "domain": domain_var.get()
                }

                # 加密敏感字段 - 使用当前实例的方法
                sensitive_fields = {
                    "password": entries[1].get().strip(),
                    "apiKey": entries[2].get().strip(),
                    "secret": entries[3].get().strip(),
                    "passphrase": entries[4].get().strip()
                }
                
                for field_name, field_value in sensitive_fields.items():
                    if field_value:
                        try:
                            data[field_name] = trading_system_instance.encrypt_data(field_value)
                        except Exception as e:
                            logging.error(f"加密{field_name}失败: {str(e)}")
                            data[field_name] = ""  # 保存空字符串
                    else:
                        data[field_name] = ""

                # 确保目录存在
                os.makedirs(os.path.dirname(os.path.abspath(config_file)), exist_ok=True)
                
                with open(config_file, "w", encoding="utf-8") as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
                    
                logging.info("账号和密钥已成功保存")
                # 显示成功提示
                if hasattr(self, 'main_root') and self.main_root:
                    self.main_root.after(0, lambda: messagebox.showinfo("保存成功", "账号和密钥已安全保存"))
                    
            except Exception as e:
                logging.error(f"保存账号配置失败: {str(e)}")
                # 显示错误提示
                if hasattr(self, 'main_root') and self.main_root:
                    self.main_root.after(0, lambda: messagebox.showerror("保存失败", f"保存配置时发生错误: {str(e)}"))
        def load_account():
            """加载保存的账号配置"""
            if not os.path.exists(config_file):
                logging.info("未找到保存的配置文件")
                return
                
            try:
                with open(config_file, "r", encoding="utf-8") as f:
                    data = json.load(f)

                # 处理非敏感字段
                entries[0].delete(0, tk.END)
                entries[0].insert(0, data.get("username", ""))
                proxy_var.set(data.get("proxy", False))
                domain_var.set(data.get("domain", "com"))
                
                # 如果有保存的配置，自动勾选保存选项
                save_var.set(True)

                # 处理并解密敏感字段
                sensitive_fields_map = {
                    1: "password",
                    2: "apiKey",
                    3: "secret",
                    4: "passphrase"
                }
                
                decryption_errors = []
                loaded_fields = []
                for entry_idx, data_key in sensitive_fields_map.items():
                    entries[entry_idx].delete(0, tk.END)
                    encrypted_value = data.get(data_key)
                    if encrypted_value:
                        try:
                            decrypted_value = trading_system_instance.decrypt_data(encrypted_value)
                            if decrypted_value:  # 只有成功解密才插入
                                entries[entry_idx].insert(0, decrypted_value)
                                loaded_fields.append(data_key)
                            else:
                                decryption_errors.append(data_key)
                        except Exception as e_decrypt:
                            logging.error(f"解密 {data_key} 失败: {str(e_decrypt)}")
                            decryption_errors.append(data_key)
                            entries[entry_idx].insert(0, "") # 留空
                    else:
                        entries[entry_idx].insert(0, "") # 如果字段不存在，则为空
                
                if decryption_errors:
                    error_msg = f"以下字段解密失败: {', '.join(decryption_errors)}\n请重新输入这些信息"
                    logging.warning(error_msg)
                    if hasattr(self, 'main_root') and self.main_root:
                        self.main_root.after(0, lambda: messagebox.showwarning("解密警告", error_msg))
                else:
                    logging.info(f"账号配置加载成功，已加载字段: {', '.join(loaded_fields)}")
                    # 显示加载成功提示
                    if hasattr(self, 'main_root') and self.main_root:
                        self.main_root.after(0, lambda: messagebox.showinfo("加载成功", "已自动加载保存的账号配置"))

            except json.JSONDecodeError as e_json:
                error_msg = f"配置文件格式错误: {str(e_json)}"
                logging.error(error_msg)
                if hasattr(self, 'main_root') and self.main_root:
                    self.main_root.after(0, lambda: messagebox.showerror("配置错误", error_msg))
            except Exception as e_load:
                error_msg = f"加载配置文件时发生错误: {str(e_load)}"
                logging.error(error_msg)
                if hasattr(self, 'main_root') and self.main_root:
                    self.main_root.after(0, lambda: messagebox.showerror("加载失败", error_msg))
        # 启动时自动加载
        load_account()
        
        def on_save_var(*args):
            """当保存选项状态改变时触发"""
            try:
                if save_var.get():
                    # 检查是否有内容需要保存
                    has_content = any(entry.get().strip() for entry in entries)
                    if has_content:
                        save_account()
                        logging.info("保存选项已启用，正在保存当前配置")
                    else:
                        logging.info("保存选项已启用，但暂无内容需要保存")
                else:
                    logging.info("保存选项已禁用")
            except Exception as e:
                logging.error(f"保存选项处理失败: {str(e)}")
                
        # 绑定保存选项变化事件
        save_var.trace_add('write', on_save_var)
        
        # 延迟保存机制，避免过于频繁的保存操作
        save_timer = None
        
        def delayed_save():
            """延迟保存，避免频繁操作"""
            nonlocal save_timer
            if save_timer:
                root.after_cancel(save_timer)
            save_timer = root.after(1000, lambda: save_account() if save_var.get() else None)  # 1秒后保存
        
        # 为所有输入框绑定变化事件，延迟保存
        def on_entry_change(*args):
            """当输入框内容改变时，如果启用了保存选项则延迟保存"""
            try:
                if save_var.get():
                    delayed_save()
            except Exception as e:
                logging.error(f"自动保存失败: {str(e)}")
                
        # 为所有输入框绑定延迟保存
        for entry in entries:
            if hasattr(entry, 'bind'):
                entry.bind('<KeyRelease>', on_entry_change)
                entry.bind('<FocusOut>', on_entry_change)

        # 网络诊断功能保持不变
        def on_net():
            import requests
            try:
                resp = requests.get("https://www.okx.com/api/v5/public/time", timeout=5)
                if resp.status_code == 200:
                    messagebox.showinfo("网络诊断", "网络连接正常！")
                else:
                    messagebox.showwarning("网络诊断", f"网络异常，状态码：{resp.status_code}")
            except Exception as e:
                self.smart_error_handler("网络诊断", f"网络连接失败：{str(e)}")

        # 登录和API功能：详细异常提示+代理/域名支持+自动处理ACCEPT ALL弹窗
        def get_exchange():
            if self.exchange:
                return self.exchange

            import ccxt
            proxies = None
            if proxy_var.get():
                proxies = {'http': 'http://127.0.0.1:10808', 'https': 'http://127.0.0.1:10808'}
            # 强制区分模拟盘和实盘API
            sim_mode = tabControl.index(tabControl.select()) == 0
            api_kwargs = {
                'apiKey': entries[2].get().strip(),
                'secret': entries[3].get().strip(),
                'password': entries[4].get().strip(),
                'enableRateLimit': True,
                'proxies': proxies,
                'options': {
                    'defaultType': 'swap',  # 设置默认为合约交易
                    'createMarketBuyOrderRequiresPrice': False,  # 允许市价买单不需要价格
                }
            }
            if sim_mode:
                # 模拟盘设置 - 使用正确的OKX模拟交易API设置
                logging.info("使用模拟盘模式")
                api_kwargs['hostname'] = 'www.okx.com'

                # 添加模拟交易必要的选项
                api_kwargs['options']['test'] = True  # 启用模拟交易
                api_kwargs['options']['adjustForTimeDifference'] = True
                api_kwargs['options']['recvWindow'] = 10000
                api_kwargs['options']['defaultType'] = 'swap'  # 确保默认为合约交易

                # 创建交易所实例
                self.exchange = ccxt.okx(api_kwargs)

                # 设置沙盒模式 - 必须在创建实例后立即设置
                self.exchange.set_sandbox_mode(True)

                # 确保加载市场数据前已设置沙盒模式
                logging.info("已启用OKX模拟交易模式，设置选项：" + str(api_kwargs['options']))

                # --- 开始：尝试设置账户为双向持仓模式 ---
                try:
                    logging.info("尝试为模拟盘账户设置双向持仓模式 (long_short_mode)...")
                    # OKEx API: POST /api/v5/account/set-position-mode
                    # params: {"posMode": "long_short_mode"}
                    # ccxt的set_position_mode方法（如果存在且工作正常）
                    if hasattr(self.exchange, 'set_position_mode'):
                        # 第一个参数 posMode ('long_short_mode' 或 'net_mode')
                        # 第二个参数 params (可选, 可用于指定 instType 等)
                        # 对于账户级别，通常不需要额外params
                        response = self.exchange.set_position_mode('long_short_mode', {'instType': 'SWAP'}) # 尝试为SWAP合约设置
                        logging.info(f"调用 ex.set_position_mode('long_short_mode') 响应: {response}")
                        # 检查响应是否成功，OKX成功时code通常是"0"
                        if isinstance(response, dict) and response.get('code') == '0':
                             logging.info("通过 ex.set_position_mode() 成功设置账户为双向持仓模式。")
                        elif isinstance(response, dict) and response.get('code') != '0':
                            # 检查是否已经是该模式
                            msg_lower = response.get('msg', '').lower()
                            if "position mode is already set" in msg_lower or "the account position mode is the same" in msg_lower:
                                logging.info(f"账户已经是双向持仓模式或API不允许重复设置: {response.get('msg')}")
                            else:
                                logging.warning(f"API设置双向持仓模式失败: {response.get('msg', '未知错误')}")
                                messagebox.showwarning("账户模式警告", f"尝试通过API设置双向持仓模式失败: {response.get('msg', '未知错误')}\n请手动前往OKEx模拟盘官网，在\"交易设置\"中将\"持仓方式\"更改为\"双向持仓\"。否则合约交易可能失败。")
                        else: # 响应格式未知或不明确
                            logging.info("ex.set_position_mode() 响应格式未知，假定可能成功或无需设置。")

                    elif hasattr(self.exchange, 'privatePostAccountSetPositionMode'): # 备用：使用隐式方法
                        response = self.exchange.privatePostAccountSetPositionMode({'posMode': 'long_short_mode', 'instType': 'SWAP'})
                        logging.info(f"通过 privatePostAccountSetPositionMode 尝试设置双向持仓模式，响应: {response}")
                        if response and response.get('code') == '0' and response.get('data') and response['data'][0].get('posMode') == 'long_short_mode':
                            logging.info("成功设置账户为双向持仓模式 (long_short_mode)。")
                        elif response and response.get('code') != '0':
                            msg_lower = response.get('msg', '').lower()
                            if "position mode is already set" in msg_lower or "the account position mode is the same" in msg_lower:
                                logging.info(f"账户已经是双向持仓模式或API不允许重复设置: {response.get('msg')}")
                            else:
                                logging.warning(f"API设置双向持仓模式失败: {response.get('msg', '未知错误')}")
                                messagebox.showwarning("账户模式警告", f"尝试通过API设置双向持仓模式失败: {response.get('msg', '未知错误')}\n请手动前往OKEx模拟盘官网，在\"交易设置\"中将\"持仓方式\"更改为\"双向持仓\"。否则合约交易可能失败。")
                        else:
                            logging.warning(f"API设置双向持仓模式的响应不明确: {response}")
                            messagebox.showwarning("账户模式警告", f"尝试通过API设置双向持仓模式的响应不明确。\n请手动前往OKEx模拟盘官网，在\"交易设置\"中将\"持仓方式\"更改为\"双向持仓\"。否则合约交易可能失败。")
                    else:
                        logging.warning("当前 ccxt 版本不直接支持 set_position_mode 或 privatePostAccountSetPositionMode。")
                        messagebox.showwarning("账户模式警告", "无法通过API自动设置账户为双向持仓模式。\n请手动前往OKEx模拟盘官网，在\"交易设置\"中将\"持仓方式\"更改为\"双向持仓\"。否则合约交易可能失败。")

                except ccxt.NetworkError as e_pos_mode:
                    self.smart_error_handler("网络错误", f"设置账户持仓模式时发生网络错误: {str(e_pos_mode)}")
                    return None
                except ccxt.ExchangeError as e_pos_mode:
                    logging.error(f"设置持仓模式时交易所错误: {str(e_pos_mode)}")
                    msg_lower = str(e_pos_mode).lower()
                    if "position mode is already set" in msg_lower or "the account position mode is the same" in msg_lower:
                        logging.info(f"账户已经是双向持仓模式或API不允许重复设置: {str(e_pos_mode)}")
                    elif "permission denied" in msg_lower or "not support" in msg_lower or "10008" in str(e_pos_mode): # 10008: API key permission issue
                         logging.warning(f"API密钥无权设置持仓模式或模拟盘不支持此操作: {str(e_pos_mode)}")
                         messagebox.showwarning("账户模式警告", f"API密钥无权设置持仓模式或模拟盘不支持此操作。\n错误: {str(e_pos_mode)}\n请手动前往OKEx模拟盘官网，在\"交易设置\"中将\"持仓方式\"更改为\"双向持仓\"。否则合约交易可能失败。")
                    else:
                        messagebox.showwarning("账户模式警告", f"尝试设置双向持仓模式失败: {str(e_pos_mode)}\n请手动前往OKEx模拟盘官网，在\"交易设置\"中将\"持仓方式\"更改为\"双向持仓\"。否则合约交易可能失败。")
                except Exception as e_pos_mode:
                    logging.error(f"设置持仓模式时发生未知错误: {str(e_pos_mode)}")
                    messagebox.showwarning("账户模式警告", f"尝试设置双向持仓模式时发生未知错误: {str(e_pos_mode)}\n请手动前往OKEx模拟盘官网，在\"交易设置\"中将\"持仓方式\"更改为\"双向持仓\"。否则合约交易可能失败。")
                # --- 结束：尝试设置账户为双向持仓模式 ---

                # 验证模拟盘连接
                try:
                    self._get_cached_markets(True) # 使用缓存机制加载市场数据
                    balance = self.exchange.fetch_balance()
                    logging.info(f"模拟盘连接成功，账户余额: {balance['total'].get('USDT', 0)} USDT")
                    
                    # 智能交易引擎相关代码已删除
                    logging.info("API连接验证成功")
                except Exception as e:
                    self.smart_error_handler("API连接", f"模拟盘连接或获取余额失败: {str(e)}\n请检查API密钥和网络设置。")
                    return None
            else:
                # 实盘设置
                logging.info("使用实盘模式")
                domain = domain_var.get()
                api_kwargs['hostname'] = f'okx.{domain}' if domain != 'com' else 'okx.com'
                self.exchange = ccxt.okx(api_kwargs)
                self.exchange.set_sandbox_mode(False)

                # 验证实盘连接
                try:
                    self._get_cached_markets()
                    balance = self.exchange.fetch_balance()
                    logging.info(f"实盘连接成功，账户余额: {balance['total'].get('USDT', 0)} USDT")
                    
                    # 智能交易引擎相关代码已删除
                    logging.info("API连接验证成功")
                except Exception as e:
                    self.smart_error_handler("API连接", f"实盘连接失败: {str(e)}\n请检查API密钥和网络设置。")
                    return None

            # 自动接受ACCEPT ALL弹窗（如遇到）
            if hasattr(self.exchange, 'accept_cookies'):
                try:
                    self.exchange.accept_cookies()
                except Exception:
                    pass

            return self.exchange
        def on_test_api():
            if not (entries[2].get().strip() and entries[3].get().strip() and entries[4].get().strip()):
                messagebox.showwarning("提示", "请填写API密钥、Secret Key和Passphrase！")
                return
            try:
                exchange = get_exchange()
                self._get_cached_markets()
                # 用fetch_balance测试API有效性，若失败详细提示
                balance = exchange.fetch_balance()
                asset_info = balance['total'] if 'total' in balance else balance
                messagebox.showinfo("API密钥有效", f"API密钥验证成功！\n资产信息：{asset_info}")
            except Exception as e:
                self.smart_error_handler("API验证", f"API密钥验证异常: {str(e)}")
        def on_login():
            if not all(e.get().strip() for e in entries[:5]):
                messagebox.showwarning("提示", "请填写完整的账号、密码和API密钥信息！")
                return
            sim_mode = tabControl.index(tabControl.select()) == 0
            # 先检测网络
            if not self.check_network(proxies={'http': 'http://127.0.0.1:10808', 'https': 'http://127.0.0.1:10808'} if proxy_var.get() else None):
                self.smart_error_handler("网络连接", "无法连接欧易OKX，请检查网络或代理设置！")
                return
            try:
                import ccxt
                proxies = {'http': 'http://127.0.0.1:10808', 'https': 'http://127.0.0.1:10808'} if proxy_var.get() else None
                exchange = get_exchange()
                self._get_cached_markets()
                balance = exchange.fetch_balance()
                if not balance or 'total' not in balance:
                    raise Exception("API密钥无效或资产信息获取失败")
                if sim_mode:
                    messagebox.showinfo("登录成功", "模拟盘API密钥验证通过，登录成功！\n所有交易均在OKX模拟盘官网可查！")
                else:
                    messagebox.showinfo("登录成功", "实盘API密钥验证通过，登录成功！")
                if save_var.get():
                    save_account()
                # 销毁登录窗口，避免重复窗口
                if self.login_root:
                    self.login_root.destroy()
                    self.login_root = None
                self.show_main_window(exchange)
                return
            except Exception as e:
                import traceback
                tb = traceback.format_exc()
                messagebox.showerror("登录异常", f"{str(e)}\n---\n{tb}")
        def on_clear():
            for e in entries:
                e.delete(0, tk.END)
        def on_help():
            messagebox.showinfo("帮助", "帮助功能待实现")

        ttk.Button(tab_sim, text="验证 API 密钥", command=on_test_api).place(x=20, y=320, width=110)
        ttk.Button(tab_sim, text="登录", command=on_login).place(x=150, y=320, width=90)
        ttk.Button(tab_sim, text="清除配置", command=on_clear).place(x=260, y=320, width=90)
        ttk.Button(tab_sim, text="网络诊断", command=on_net).place(x=370, y=320, width=90)
        ttk.Button(tab_sim, text="帮助", command=on_help).place(x=480, y=320, width=60)

        # 不再显示模拟盘API说明提示

        root.mainloop()

    def show_main_window(self, exchange):
        import tkinter as tk
        from tkinter import ttk, messagebox
        import threading, time

        # 确保只有一个主窗口存在
        if self.main_root is not None:
            try:
                self.main_root.destroy()
            except:
                pass
            self.main_root = None
        
        # 判断是否是模拟盘
        is_sandbox = exchange.options.get('test', False)
        mode_text = "模拟盘" if is_sandbox else "实盘"

        self.main_root = tk.Tk() # 保存为实例属性
        self.main_root.title(f"欧易智能全自动交易系统 V1.398 - {mode_text}模式")
        self.main_root.geometry("1350x420")

        # 顶部标签与资产 - 确保模拟盘显示蓝色，实盘显示红色
        # 在模拟交易模式下，显示"模拟盘交易模式"，颜色为蓝色
        # 在实盘交易模式下，显示"实盘交易模式"，颜色为红色
        title_label = tk.Label(self.main_root, text=f"{mode_text}交易模式", font=("微软雅黑", 12, "bold"), fg="blue" if is_sandbox else "red")
        title_label.place(x=10, y=5)

        # 确保整个界面都知道当前是模拟交易模式
        self.main_root.is_sandbox = is_sandbox
        def update_asset():
            try:
                balance = exchange.fetch_balance()
                asset_value = balance['total'].get('USDT', 0)
                try:
                    self.asset_label.config(text=f"账户资产总值: {asset_value:.2f} USDT", fg="red" if asset_value==0 else "black") # 使用 self.asset_label
                except Exception as e:
                    # 界面可能已关闭，忽略错误
                    logging.debug(f"更新资产标签失败 (可能已关闭): {str(e)}") # 改为 debug
            except Exception as e:
                try:
                    self.asset_label.config(text=f"账户资产总值: 查询失败", fg="red") # 使用 self.asset_label, 移除错误详情 e
                except Exception as e2:
                    # 界面可能已关闭，忽略错误
                    logging.debug(f"更新资产标签失败 (可能已关闭): {str(e2)}") # 改为 debug
        self.asset_label = tk.Label(self.main_root, text="账户资产总值:", font=("微软雅黑", 11)) # 保存为实例属性
        self.asset_label.place(x=180, y=5)
        update_asset() # update_asset 内部会用 self.asset_label
        # 操作按钮和状态栏
        columns = [
            "序号", "状态", "类型", "选择策略", "交易策略", "交易品种", "最新价格", "建仓价格", "建仓时间", "平仓价格", "平仓时间", "持仓时间", "方向 (多/空)",
            "数量", "倍数", "手续费", "盈亏", "收益率 %", "占用账户资金", "占用账户资金比例", "账户余额"
        ]
        frame = tk.Frame(self.main_root)
        frame.place(x=10, y=35, width=1320, height=340)
        self.positions_tree = ttk.Treeview(frame, columns=columns, show="headings", height=12) # 保存为实例属性
        for col in columns:
            self.positions_tree.heading(col, text=col)
            # 根据列内容自动调整列宽，使表格更紧凑
            if col == "序号":
                self.positions_tree.column(col, width=40, anchor="center")
            elif col == "状态":
                self.positions_tree.column(col, width=60, anchor="center")
            elif col == "类型":
                self.positions_tree.column(col, width=50, anchor="center")
            elif col == "选择策略":
                self.positions_tree.column(col, width=80, anchor="center")
            elif col == "交易策略":
                self.positions_tree.column(col, width=80, anchor="center")
            elif col == "交易品种":
                self.positions_tree.column(col, width=115, anchor="center") # 增加宽度以测试滚动条
            elif col == "最新价格" or col == "建仓价格" or col == "平仓价格":
                self.positions_tree.column(col, width=70, anchor="center")
            elif col == "建仓时间" or col == "平仓时间":
                self.positions_tree.column(col, width=120, anchor="center")
            elif col == "持仓时间":
                self.positions_tree.column(col, width=60, anchor="center")
            elif col == "方向 (多/空)" or col == "数量" or col == "倍数" or col == "手续费":
                self.positions_tree.column(col, width=60, anchor="center")
            elif col == "策略":
                self.positions_tree.column(col, width=60, anchor="center")
            elif col == "盈亏" or col == "收益率 %" or col == "占用账户资金" or col == "占用账户资金比例":
                self.positions_tree.column(col, width=80, anchor="center")
            elif col == "账户余额":
                self.positions_tree.column(col, width=90, anchor="center")
            else:
                self.positions_tree.column(col, width=70, anchor="center")
        vsb = ttk.Scrollbar(frame, orient="vertical", command=self.positions_tree.yview)
        hsb = ttk.Scrollbar(frame, orient="horizontal", command=self.positions_tree.xview)
        self.positions_tree.configure(yscrollcommand=vsb.set, xscrollcommand=hsb.set)
        self.positions_tree.grid(row=0, column=0, sticky='nsew')
        vsb.grid(row=0, column=1, sticky='ns')
        hsb.grid(row=1, column=0, sticky='ew')
        frame.grid_rowconfigure(0, weight=1)
        frame.grid_columnconfigure(0, weight=1)
        # 初始化表格 - 动态行数设计
        self.display_rows_limit = 10  # 默认显示10行正式交易品种
        self.close_rows_limit = 5     # 默认显示5行平仓品种
        
        # 添加建仓交易行（交易1-交易10）
        for i in range(self.display_rows_limit):
            val = [f"交易{i+1}", "待机", ""] + ["" for _ in range(len(columns)-3)]
            self.positions_tree.insert("", "end", values=val)
        
        # 添加平仓交易行（平仓1-平仓N）
        for i in range(self.close_rows_limit):
            val = [f"平仓{i+1}", "待机", ""] + ["" for _ in range(len(columns)-3)]
            self.positions_tree.insert("", "end", values=val)
        # 状态栏
        self.main_status_var = tk.StringVar() # 保存为实例属性
        self.main_status_var.set("已停止")
        tk.Label(self.main_root, textvariable=self.main_status_var, fg="blue").place(x=950, y=5)
        
        # 智能选择品种按钮
        # selected_symbols = [] # 由新的 self.selected_symbols_for_trading 替代
        # open_prices = {}  # 记录开仓价，现在由 trade_status_details 管理

        def on_smart_select():
            try:
                self.main_status_var.set("正在智能选择品种并准备启动自动交易...")
            except Exception as e:
                logging.error(f"更新状态标签失败: {str(e)}")

            try:
                self.btn_smart_select.config(state="disabled")
            except Exception as e:
                logging.error(f"禁用按钮失败: {str(e)}")

            def select_thread():
                try:
                    # 清空表格中的非持仓行数据
                    # 这个逻辑在 update_main_trading_table 中已经有了，这里主要是确保视觉上清爽
                    # for item_id in self.positions_tree.get_children():
                    #    item_values = self.positions_tree.item(item_id, 'values')
                    #    # 假设第二列是品种名称，如果为空或者不在当前持仓中，则清空
                    #    if not item_values[1] or item_values[1] not in self.current_positions:
                    #        self.positions_tree.item(item_id, values=[item_values[0]] + [""] * (len(item_values) -1) )
                    # 简化：直接在 update_main_trading_table 中处理显示
                    logging.info("准备执行智能选择品种...")
                    
                    # 获取当前实际持仓，用于判断还能开多少仓位
                    current_positions_symbols = []
                    try:
                        fetched_positions = self.fetch_positions(self.exchange)
                        if fetched_positions:
                            current_positions_symbols = [p['symbol'] for p in fetched_positions if float(p.get('contracts', p.get('amount', 0))) > 0]
                        logging.info(f"当前实际持仓品种: {current_positions_symbols}")
                    except Exception as e_fetch_pos:
                        logging.error(f"智能选择前获取持仓失败: {e_fetch_pos}")
                        # 如果获取失败，保守起见，假设已达上限，或依赖配置

                    selected_symbols_list = [] # 重命名以避免与外部作用域的 selected_symbols 混淆
                    if self.exchange:
                        num_current_positions = len(current_positions_symbols)
                        max_new_selection = self.config.get('max_positions', 5) - num_current_positions
                        
                        if max_new_selection <= 0:
                            logging.info("已达到或超过最大持仓数量，本次不选择新品种。")
                            selected_symbols_list = []
                        else:
                            logging.info(f"准备调用选品策略，最多选择 {max_new_selection} 个新品种。")
                            # 使用统一的选品策略接口
                            try:
                                from strategy_core import execute_select_product_strategy
                                selected_symbols_list, strategy_name = execute_select_product_strategy(
                                    exchange=self.exchange,
                                    symbols=None,  # 让策略自己获取可用交易对
                                    limit=max_new_selection  # 根据可用持仓数量限制选择数量
                                )
                                
                                # 转换为交易所格式
                                if selected_symbols_list:
                                    converted_symbols = []
                                    for symbol in selected_symbols_list:
                                        if symbol.endswith('-USDT'):
                                            converted_symbol = symbol.replace('-', '/') + ':USDT'
                                        elif '/' in symbol and not symbol.endswith(':USDT'):
                                            converted_symbol = symbol + ':USDT'
                                        else:
                                            converted_symbol = symbol
                                        converted_symbols.append(converted_symbol)
                                    selected_symbols_list = converted_symbols
                                
                                self.selection_strategy_name = strategy_name # 保存选择策略的名称
                                logging.info(f"手动智能选品成功，策略: {strategy_name}，选择品种: {selected_symbols_list}")
                            except Exception as e:
                                logging.error(f"调用选品策略失败: {str(e)}，使用配置的降级候选")
                                fallback_symbols = self.config.get("fallback_symbols", ["BTC/USDT:USDT", "ETH/USDT:USDT", "BNB/USDT:USDT"])
                                # 过滤已持仓品种
                                active_symbols = [pos['symbol'] for pos in self.exchange.fetch_positions() if pos['contracts'] != 0] if self.exchange else []
                                selected_symbols_list = [s for s in fallback_symbols if s not in active_symbols][:max_new_selection]
                                strategy_name = 'fallback_strategy'
                                self.selection_strategy_name = strategy_name
                                logging.info(f"GUI降级方案选出: {selected_symbols_list}")
                                
                                # 记录GUI降级事件
                                if not hasattr(self, 'gui_fallback_count'):
                                    self.gui_fallback_count = 0
                                self.gui_fallback_count += 1

                        logging.info(f"智能模块 '{self.selection_strategy_name}' 选择了 {len(selected_symbols_list)} 个品种: {selected_symbols_list}")
                    else:
                        logging.error("Exchange object is not initialized. Cannot select symbols.")
                    
                    self.selected_symbols_for_trading = selected_symbols_list # 更新待交易品种列表

                    # 为新选出的品种记录选择策略
                    for symbol in self.selected_symbols_for_trading:
                        if symbol not in self.trade_status_details:
                            self.trade_status_details[symbol] = {}
                        self.trade_status_details[symbol]['selection_strategy'] = self.selection_strategy_name
                        self.trade_status_details[symbol]['status'] = 'selected'
                    
                    if not self.selected_symbols_for_trading and (not self.exchange or max_new_selection > 0) :
                        logging.warning("智能选择未能选出任何新的交易品种。")
                        self.main_root.after(0, lambda: self.update_status_bar("智能选择未能选出任何新的交易品种。请检查日志。"))
                    
                    # 在主线程中更新UI和启动交易
                    def update_ui_and_start_trading():
                        try:
                            self.main_status_var.set("智能选择完成，自动交易已激活/更新")
                            self.btn_smart_select.config(state="normal")
                            
                            self._is_trading = True
                            self._is_paused = False
                            
                            # 如果交易线程未运行，则启动它
                            if not hasattr(self, 'trade_thread') or not self.trade_thread.is_alive():
                                logging.info("自动交易线程未运行，正在启动...")
                                self.auto_trading() # auto_trading 内部会创建和启动线程
                            else:
                                logging.info("自动交易线程已在运行中。新的选择将在下一轮生效。")
                            
                            if self.selected_symbols_for_trading:
                                messagebox.showinfo("智能选择完成", f"已选择新的交易品种：{', '.join(self.selected_symbols_for_trading)}\n自动交易已激活/更新！")
                            else:
                                messagebox.showinfo("智能选择提示", "本次智能选择未选出新的交易品种。自动交易继续运行。")
                        except Exception as e_ui_start:
                            logging.error(f"更新UI或启动交易失败: {str(e_ui_start)}")

                    self.main_root.after(0, update_ui_and_start_trading)

                except Exception as e_select_outer:
                    logging.error(f"智能选择线程发生错误: {str(e_select_outer)}")
                    logging.error(traceback.format_exc())
                    def show_error_outer():
                        try:
                            self.main_status_var.set("已停止 (选择错误)")
                            self.btn_smart_select.config(state="normal")
                            messagebox.showerror("智能选择失败", f"执行智能选择时发生错误：{str(e_select_outer)}")
                        except Exception as e2_outer:
                            logging.error(f"显示外部错误对话框失败: {str(e2_outer)}")
                    self.main_root.after(0, show_error_outer)

            thread = threading.Thread(target=select_thread, daemon=True, name="SmartSelectV2")
            self._register_thread(thread)
            thread.start()

        # 智能选择品种按钮
        self.btn_smart_select = ttk.Button(self.main_root, text="智能选择品种", width=14, command=on_smart_select) # 保存为实例属性
        self.btn_smart_select.place(x=500, y=2)

        # 策略配置管理按钮
        self.btn_strategy_config = ttk.Button(self.main_root, text="策略配置", width=10, command=self.show_strategy_config_window)
        self.btn_strategy_config.place(x=620, y=2)

        # 自动交易功能 - 程序启动后自动开始交易
        # def auto_trading(): # 将被移动到类级别
        #     # 设置交易状态
        #     self.main_root._trading = True # 使用 self.main_root
        #     self.main_root._paused = False
        # def pause_trading(): # 已移到类级别
        # def stop_trading(): # 已移到类级别
        
        # 移除执行交易按钮，改为自动交易
        self.btn_pause = ttk.Button(self.main_root, text="全暂停交易", width=12, command=self.pause_trading) # 修改文字和宽度
        self.btn_pause.place(x=720, y=2)
        self.btn_stop = ttk.Button(self.main_root, text="全停止交易", width=12, command=self.stop_trading) # 修改文字和宽度
        self.btn_stop.place(x=850, y=2) # 调整x位置以适应新宽度

        # 移除自动启动交易 - 改为用户主动点击"智能选择品种"按钮开始交易
        # self.main_root.after(2000, self.auto_trading) # 已移除自动启动
        
        # 执行启动后任务（如果存在）
        if hasattr(self, 'post_startup_tasks'):
            self.main_root.after(3000, self.post_startup_tasks)
        
        self.main_root.mainloop()

    def show_strategy_config_window(self):
        """显示策略配置管理窗口"""
        import tkinter as tk
        from tkinter import ttk, messagebox
        from strategy_core import strategy_core as strategy_manager, list_available_strategies, get_strategy_performance_report
        
        # 确保只有一个配置窗口存在
        if hasattr(self, 'config_window') and self.config_window and self.config_window.winfo_exists():
            self.config_window.lift()
            return
        
        self.config_window = tk.Toplevel(self.main_root)
        self.config_window.title("策略配置管理")
        self.config_window.geometry("900x600")
        self.config_window.resizable(True, True)
        
        # 居中显示窗口
        self.config_window.update_idletasks()
        x = (self.config_window.winfo_screenwidth() // 2) - (900 // 2)
        y = (self.config_window.winfo_screenheight() // 2) - (600 // 2)
        self.config_window.geometry(f"900x600+{x}+{y}")
        
        # 创建主框架
        main_frame = ttk.Frame(self.config_window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建选项卡
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill=tk.BOTH, expand=True)
        
        # 选品策略配置选项卡
        select_frame = ttk.Frame(notebook)
        notebook.add(select_frame, text="选品策略配置")
        
        # 交易策略配置选项卡
        trading_frame = ttk.Frame(notebook)
        notebook.add(trading_frame, text="交易策略配置")
        
        # 性能监控选项卡
        performance_frame = ttk.Frame(notebook)
        notebook.add(performance_frame, text="性能监控")
        
        # 创建选品策略配置界面
        self._create_strategy_config_tab(select_frame, "select_product")
        
        # 创建交易策略配置界面
        self._create_strategy_config_tab(trading_frame, "trading")
        
        # 创建性能监控界面
        self._create_performance_tab(performance_frame)
        
        # 底部按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(button_frame, text="保存配置", command=self._save_strategy_config).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="重载配置", command=self._reload_strategy_config).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="导出报告", command=self._export_performance_report).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="关闭", command=self.config_window.destroy).pack(side=tk.RIGHT)
    
    def _create_strategy_config_tab(self, parent, strategy_type):
        """创建策略配置选项卡内容"""
        import tkinter as tk
        from tkinter import ttk
        
        # 创建滚动框架
        canvas = tk.Canvas(parent)
        scrollbar = ttk.Scrollbar(parent, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # 获取策略配置
        try:
            config = strategy_manager.config.get("strategies", {}).get(strategy_type, {})
            strategy_title = "选品策略" if strategy_type == "select_product" else "交易策略"
            
            # 标题
            title_label = ttk.Label(scrollable_frame, text=f"{strategy_title}配置", font=("微软雅黑", 14, "bold"))
            title_label.pack(pady=(0, 20))
            
            # 策略列表
            strategies = {
                "select_product": [
                    ("选品策略一", "select_symbols_by_atr_adx", "基于ATR和ADX指标的选品策略"),
                    ("选品策略二", "smart_select_symbols_v2", "智能选品策略V2"),
                    ("选品策略三", "select_positions_to_close", "持仓平仓选择策略"),
                    ("选品策略四", "smart_select_symbols", "智能选品策略")
                ],
                "trading": [
                    ("交易策略一", "calculate_indicators", "技术指标计算策略"),
                    ("交易策略二", "adaptive_strategy", "自适应交易策略"),
                    ("交易策略三", "smart_trading_strategy", "智能交易策略"),
                    ("交易策略四", "smart_stop_loss", "智能止损策略")
                ]
            }
            
            # 存储控件引用
            if not hasattr(self, 'strategy_widgets'):
                self.strategy_widgets = {}
            if strategy_type not in self.strategy_widgets:
                self.strategy_widgets[strategy_type] = {}
            
            for strategy_name, func_name, description in strategies[strategy_type]:
                # 策略框架
                strategy_frame = ttk.LabelFrame(scrollable_frame, text=strategy_name, padding=10)
                strategy_frame.pack(fill=tk.X, pady=5)
                
                # 策略描述
                desc_label = ttk.Label(strategy_frame, text=description, foreground="gray")
                desc_label.pack(anchor=tk.W)
                
                # 启用/禁用
                enabled_var = tk.BooleanVar()
                strategy_config = config.get(strategy_name, {})
                enabled_var.set(strategy_config.get("enabled", True))
                
                enabled_check = ttk.Checkbutton(strategy_frame, text="启用此策略", variable=enabled_var)
                enabled_check.pack(anchor=tk.W, pady=(5, 0))
                
                # 参数配置框架
                params_frame = ttk.Frame(strategy_frame)
                params_frame.pack(fill=tk.X, pady=(10, 0))
                
                # 常用参数配置
                param_widgets = {}
                
                # 根据策略类型添加相应参数
                if strategy_type == "select_product":
                    # 选品策略参数
                    params = [
                        ("max_symbols", "最大选择数量", "int", strategy_config.get("max_symbols", 5)),
                        ("min_volume", "最小成交量", "float", strategy_config.get("min_volume", 1000000)),
                        ("risk_level", "风险等级(1-5)", "int", strategy_config.get("risk_level", 3))
                    ]
                else:
                    # 交易策略参数
                    params = [
                        ("leverage", "杠杆倍数", "int", strategy_config.get("leverage", 10)),
                        ("stop_loss", "止损比例(%)", "float", strategy_config.get("stop_loss", 5.0)),
                        ("take_profit", "止盈比例(%)", "float", strategy_config.get("take_profit", 10.0)),
                        ("position_size", "仓位大小(%)", "float", strategy_config.get("position_size", 20.0))
                    ]
                
                for param_name, param_label, param_type, default_value in params:
                    param_row = ttk.Frame(params_frame)
                    param_row.pack(fill=tk.X, pady=2)
                    
                    ttk.Label(param_row, text=param_label + ":", width=15).pack(side=tk.LEFT)
                    
                    if param_type == "int":
                        var = tk.IntVar(value=int(default_value) if default_value else 0)
                        entry = ttk.Entry(param_row, textvariable=var, width=10)
                    elif param_type == "float":
                        var = tk.DoubleVar(value=float(default_value) if default_value else 0.0)
                        entry = ttk.Entry(param_row, textvariable=var, width=10)
                    else:
                        var = tk.StringVar(value=str(default_value) if default_value else "")
                        entry = ttk.Entry(param_row, textvariable=var, width=20)
                    
                    entry.pack(side=tk.LEFT, padx=(5, 0))
                    param_widgets[param_name] = var
                
                # 保存控件引用
                self.strategy_widgets[strategy_type][strategy_name] = {
                    'enabled': enabled_var,
                    'params': param_widgets
                }
                
        except Exception as e:
            error_label = ttk.Label(scrollable_frame, text=f"加载策略配置失败: {str(e)}", foreground="red")
            error_label.pack(pady=20)
            logging.error(f"创建策略配置选项卡失败: {str(e)}")
    
    def _create_performance_tab(self, parent):
        """创建性能监控选项卡内容"""
        import tkinter as tk
        from tkinter import ttk
        
        # 性能统计表格
        columns = ["策略名称", "调用次数", "成功次数", "失败次数", "成功率", "平均耗时(ms)", "最后执行"]
        
        tree_frame = ttk.Frame(parent)
        tree_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        self.performance_tree = ttk.Treeview(tree_frame, columns=columns, show="headings", height=15)
        
        for col in columns:
            self.performance_tree.heading(col, text=col)
            if col == "策略名称":
                self.performance_tree.column(col, width=150)
            elif col in ["调用次数", "成功次数", "失败次数"]:
                self.performance_tree.column(col, width=80, anchor="center")
            elif col in ["成功率", "平均耗时(ms)"]:
                self.performance_tree.column(col, width=100, anchor="center")
            else:
                self.performance_tree.column(col, width=120, anchor="center")
        
        # 滚动条
        perf_scrollbar = ttk.Scrollbar(tree_frame, orient="vertical", command=self.performance_tree.yview)
        self.performance_tree.configure(yscrollcommand=perf_scrollbar.set)
        
        self.performance_tree.pack(side="left", fill="both", expand=True)
        perf_scrollbar.pack(side="right", fill="y")
        
        # 刷新按钮
        refresh_frame = ttk.Frame(parent)
        refresh_frame.pack(fill=tk.X)
        
        ttk.Button(refresh_frame, text="刷新数据", command=self._refresh_performance_data).pack(side=tk.LEFT)
        ttk.Button(refresh_frame, text="清除历史", command=self._clear_performance_data).pack(side=tk.LEFT, padx=(10, 0))
        
        # 初始加载性能数据
        self._refresh_performance_data()
    
    def _refresh_performance_data(self):
        """刷新性能监控数据"""
        try:
            from strategy_performance import performance_monitor
            
            # 清空现有数据
            for item in self.performance_tree.get_children():
                self.performance_tree.delete(item)
            
            # 获取性能数据
            from strategy_core import strategy_core
            performance_data = strategy_core.performance_monitor.get_all_performance_data()
            
            for strategy_name, data in performance_data.items():
                total_calls = data.get('total_calls', 0)
                success_count = data.get('success_count', 0)
                error_count = data.get('error_count', 0)
                success_rate = (success_count / total_calls * 100) if total_calls > 0 else 0
                avg_time = data.get('average_time', 0) * 1000  # 转换为毫秒
                last_execution = data.get('last_execution', 'N/A')
                
                if isinstance(last_execution, str) and last_execution != 'N/A':
                    try:
                        from datetime import datetime
                        dt = datetime.fromisoformat(last_execution)
                        last_execution = dt.strftime('%Y-%m-%d %H:%M:%S')
                    except:
                        pass
                
                values = [
                    strategy_name,
                    str(total_calls),
                    str(success_count),
                    str(error_count),
                    f"{success_rate:.1f}%",
                    f"{avg_time:.2f}",
                    last_execution
                ]
                
                self.performance_tree.insert("", "end", values=values)
                
        except Exception as e:
            logging.error(f"刷新性能数据失败: {str(e)}")
            messagebox.showerror("错误", f"刷新性能数据失败: {str(e)}")
    
    def _clear_performance_data(self):
        """清除性能监控历史数据"""
        try:
            from tkinter import messagebox
            if messagebox.askyesno("确认", "确定要清除所有性能监控历史数据吗？"):
                from strategy_core import strategy_core
                strategy_core.performance_monitor.clear_all_data()
                self._refresh_performance_data()
                messagebox.showinfo("成功", "性能监控历史数据已清除")
        except Exception as e:
            logging.error(f"清除性能数据失败: {str(e)}")
            messagebox.showerror("错误", f"清除性能数据失败: {str(e)}")
    
    def _save_strategy_config(self):
        """保存策略配置"""
        try:
            if not hasattr(self, 'strategy_widgets'):
                messagebox.showwarning("警告", "没有可保存的配置")
                return
            
            # 更新配置
            for strategy_type, strategies in self.strategy_widgets.items():
                for strategy_name, widgets in strategies.items():
                    enabled = widgets['enabled'].get()
                    params = {}
                    for param_name, var in widgets['params'].items():
                        params[param_name] = var.get()
                    
                    # 更新策略配置
                    strategy_manager.update_strategy_config(
                        strategy_type, strategy_name, 
                        {'enabled': enabled, **params}
                    )
            
            # 保存到文件
            strategy_manager.save_config()
            messagebox.showinfo("成功", "策略配置已保存")
            
        except Exception as e:
            logging.error(f"保存策略配置失败: {str(e)}")
            messagebox.showerror("错误", f"保存策略配置失败: {str(e)}")
    
    def _reload_strategy_config(self):
        """重新加载策略配置"""
        try:
            strategy_manager.load_config()
            messagebox.showinfo("成功", "策略配置已重新加载")
            # 关闭当前窗口并重新打开
            if hasattr(self, 'config_window') and self.config_window:
                self.config_window.destroy()
            self.show_strategy_config_window()
        except Exception as e:
            logging.error(f"重新加载策略配置失败: {str(e)}")
            messagebox.showerror("错误", f"重新加载策略配置失败: {str(e)}")
    
    def _export_performance_report(self):
        """导出性能报告"""
        try:
            from tkinter import filedialog
            from strategy_core import strategy_core
            import json
            from datetime import datetime
            
            # 选择保存路径
            filename = filedialog.asksaveasfilename(
                defaultextension=".json",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")],
                title="导出性能报告"
            )
            
            if filename:
                # 生成报告
                report = {
                    'export_time': datetime.now().isoformat(),
                    'performance_data': strategy_core.performance_monitor.get_all_performance_data(),
                    'summary': strategy_core.performance_monitor.get_performance_summary()
                }
                
                # 保存文件
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(report, f, ensure_ascii=False, indent=2)
                
                messagebox.showinfo("成功", f"性能报告已导出到: {filename}")
                
        except Exception as e:
            logging.error(f"导出性能报告失败: {str(e)}")
            messagebox.showerror("错误", f"导出性能报告失败: {str(e)}")

    def _init_or_get_position_detail(self, symbol, quantity):
        """获取或初始化指定品种的持仓详情。"""
        detail = self.trade_status_details.get(symbol)
        if not detail:
            self.trade_status_details[symbol] = {}
            detail = self.trade_status_details[symbol]
            logging.info(f"SYNC_STATUS: New symbol {symbol} found. Initializing in trade_status_details.")
            if quantity > 0:
                detail['status'] = self.STATUS_OPEN
            else:
                detail['status'] = self.STATUS_CLOSED
            detail['strategy_name'] = self.STRATEGY_UNKNOWN
            return detail, True  # 返回 detail 和一个表示“新创建”的标志
        return detail, False

    def _update_position_entry_info(self, detail, symbol, quantity, current_time_sec):
        """更新持仓的入场信息，包括入场时间和状态。"""
        if quantity > 0 and detail.get('entry_time', 0) == 0:
            pos_timestamp_ms = detail.get('timestamp', 0)
            if pos_timestamp_ms and pos_timestamp_ms > 10000000000:  # 毫秒级
                detail['entry_time'] = pos_timestamp_ms / 1000.0
            else:  # 秒级或无效，使用当前时间
                detail['entry_time'] = current_time_sec
            logging.info(f"SYNC_STATUS: Initialized/Updated entry_time for {symbol} to {detail['entry_time']}.")
        elif quantity <= 0 and detail.get('status') not in [self.STATUS_CLOSED, self.STATUS_CLOSING, self.STATUS_CLOSED_BY_SL_TP, self.STATUS_CLOSED_BY_MANAGEMENT]:
            logging.info(f"SYNC_STATUS: Live position for {symbol} has quantity {quantity}. Marking as '{self.STATUS_CLOSED}'.")
            detail['status'] = self.STATUS_CLOSED
            if 'entry_time' not in detail:
                detail['entry_time'] = 0

    def _update_position_from_api_data(self, detail, pos_data, symbol, quantity, current_time_sec):
        """从API数据更新持仓的详细信息，并增加健壮性。"""
        if detail.get('status') != self.STATUS_OPEN:
            return False

        try:
            # 数据验证和转换
            entry_price = float(pos_data.get('entryPrice', 0))
            leverage = float(pos_data.get('leverage', 1))
            fee = float(pos_data.get('fee', 0))
            initial_margin = float(pos_data.get('initialMargin', 0))
            maintenance_margin = float(pos_data.get('maintenanceMargin', 0))
            unrealized_pnl = float(pos_data.get('unrealizedPnl', 0))
            unrealized_pnl_pct = float(pos_data.get('percentage', 0))
            mark_price = float(pos_data.get('markPrice', 0))
            index_price = float(pos_data.get('indexPrice', 0))

            # 更新持仓详情
            if 'entry_price' not in detail or detail.get('entry_price', 0) == 0:
                detail['entry_price'] = entry_price
            
            detail['quantity'] = quantity
            
            if 'side' not in detail:
                detail['side'] = pos_data.get('side')
            if 'position_side' not in detail:
                detail['position_side'] = pos_data.get('positionSide')
            
            detail['type'] = self.TYPE_CONTRACT if ":" in symbol else self.TYPE_SPOT
            
            detail['leverage'] = leverage
            detail['actual_fee'] = fee
            detail['initial_margin'] = initial_margin
            detail['maintenance_margin'] = maintenance_margin
            detail['unrealized_pnl'] = unrealized_pnl
            detail['unrealized_pnl_pct'] = unrealized_pnl_pct
            detail['mark_price'] = mark_price
            detail['index_price'] = index_price
            
            detail['api_data_complete'] = True
            detail['api_last_sync'] = current_time_sec
            logging.debug(f"SYNC_STATUS: Enhanced API data for {symbol}.")
            return True

        except (ValueError, TypeError) as e:
            logging.error(f"SYNC_ERROR: Failed to parse API data for {symbol}. Error: {e}. Data: {pos_data}")
            return False

    def _synchronize_status_details_with_live_positions(self, live_positions_list):
        """同步实时持仓数据到内部的 trade_status_details 状态字典。"""
        try:
            if not live_positions_list:
                logging.debug("SYNC_STATUS: No live positions to synchronize.")
                return

            sync_start_time = time.time()
            current_time_sec = sync_start_time
            synced_count, new_positions_count, api_data_enhanced_count = 0, 0, 0
            
            logging.info(f"SYNC_STATUS: 开始同步 {len(live_positions_list)} 个实时持仓")
            for pos_data in live_positions_list:
                symbol = pos_data.get('symbol')
                if not symbol:
                    continue

                qty_field = 'contracts' if pos_data.get('type') == 'contract' else 'amount'
                quantity = float(pos_data.get(qty_field, 0))
                
                detail, is_new = self._init_or_get_position_detail(symbol, quantity)
                if is_new:
                    new_positions_count += 1

                self._update_position_entry_info(detail, symbol, quantity, current_time_sec)
                
                if self._update_position_from_api_data(detail, pos_data, symbol, quantity, current_time_sec):
                    api_data_enhanced_count += 1

                detail['last_update'] = current_time_sec
                synced_count += 1
            
            sync_duration = time.time() - sync_start_time
            logging.info(f"SYNC_STATUS: 同步完成 - 处理 {synced_count} 个持仓 (耗时 {sync_duration:.3f}s)")
            logging.info(f"SYNC_STATUS: 详细统计 - 新发现: {new_positions_count}, API增强: {api_data_enhanced_count}")
            
            if sync_duration > 1.0:
                logging.warning(f"SYNC_PERF: 同步耗时较长 {sync_duration:.3f}s")
            elif len(live_positions_list) > 0:
                avg_time = sync_duration / len(live_positions_list)
                logging.debug(f"SYNC_PERF: 平均每个持仓同步耗时 {avg_time:.4f}s")
            logging.info(f"SYNC_STATUS: 当前 trade_status_details 包含 {len(self.trade_status_details)} 个品种")
        
        except Exception as e_sync:
            logging.error(f"SYNC_ERROR: 同步持仓数据时发生异常: {e_sync}", exc_info=True)
            try:
                if hasattr(self, 'main_status_var') and self.main_status_var:
                    self.main_status_var.set(f"同步异常: {str(e_sync)[:50]}...")
            except Exception as e_status:
                logging.error(f"SYNC_ERROR: 更新状态时发生次生异常: {e_status}")

    def auto_trading(self):
        """
        自动交易主函数，现在是 TradingSystem 的一个方法。
        """
        if not self.exchange:
            logging.error("自动交易无法启动：交易所对象未初始化。")
            try:
                if hasattr(self, 'main_status_var') and self.main_status_var:
                    self.main_status_var.set("错误：交易所未连接")
            except Exception as e_status:
                logging.error(f"AUTO_TRADING_ERROR: 更新交易所未连接状态失败: {str(e_status)}")
            return

        self._is_trading = True
        self._is_paused = False
        
        try:
            if hasattr(self, 'main_status_var') and self.main_status_var:
                self.main_status_var.set("自动交易中...")
        except Exception as e:
            logging.error(f"更新状态标签失败: {str(e)}")

        if not hasattr(self, 'trade_status_details'):
            self.trade_status_details = {} # 用于跟踪每个品种的详细交易状态

        # 导入策略函数 (可以在方法开始时导入一次)
        # 确保这些导入在文件顶部已经存在，或者在这里按需导入
        # 测试策略模块已删除，使用内置交易逻辑
        logging.info("使用内置交易策略")

        def trade_loop():
            # 导入策略函数到局部作用域
            from strategy_core import execute_trading_strategy
            
            logging.info("自动交易循环线程已启动。")
            
            # 启动实时监控器
            if self.real_time_monitor:
                self.real_time_monitor.start_monitoring(self.exchange, self.trade_status_details)
                logging.info("实时监控器已启动")
            last_symbol_selection_time = 0
            # 从配置中读取参数 - 优化为更高频和智能的交易配置
            import random
            # 智能选品间隔配置 - 性能优化版本
            base_interval = self.config.get("symbol_selection_interval", 180)  # 优化为3分钟
            interval_variance = self.config.get("interval_variance", 30)  # 减少随机性为±30秒
            symbol_selection_interval = random.randint(
                max(120, base_interval - interval_variance),  # 最少2分钟，减少频繁选品
                base_interval + interval_variance
            )
            logging.info(f"本轮选品间隔设置为: {symbol_selection_interval}秒")
            max_active_positions = self.config.get("max_active_positions", 8)  # 优化为8个持仓，减少管理复杂度
            num_to_close_on_limit = self.config.get("num_to_close_on_limit", 2)  # 优化为平仓2个位置
            
            # 动态计算每个品种的资金分配 - 总资金的5%-10%随机分配
            try:
                balance_info = self.exchange.fetch_balance()
                total_balance = balance_info['total'].get('USDT', 1000)  # 默认1000 USDT
                allocation_pct = random.uniform(0.05, 0.10)  # 5%-10%随机分配
                default_order_value_usdt = total_balance * allocation_pct
                logging.info(f"动态资金分配: 总余额 {total_balance:.2f} USDT, 单品种分配 {default_order_value_usdt:.2f} USDT ({allocation_pct*100:.1f}%)")
            except Exception as e_balance:
                logging.warning(f"获取余额失败，使用默认值: {e_balance}")
                default_order_value_usdt = self.config.get("default_order_value_usdt", 50)
            
            default_tp_pct = self.config.get("default_take_profit_pct", 2.0) # 2% 止盈
            default_sl_pct = self.config.get("default_stop_loss_pct", -1.0) # -1% 止损
            main_loop_sleep_interval = self.config.get("main_loop_sleep_interval_seconds", 3)  # 优化为3秒，减少CPU占用

            while getattr(self, '_is_trading', False): # 使用 self._is_trading
                if getattr(self, '_is_paused', False): # 使用 self._is_paused
                    try:
                        if hasattr(self, 'main_status_var') and self.main_status_var: self.main_status_var.set("已暂停")
                    except Exception: pass
                    time.sleep(main_loop_sleep_interval)
                    continue

                current_time_loop_start = time.time()
                try:
                    if not self.exchange:
                        logging.warning("auto_trading: 交易所对象丢失，跳过本次循环。")
                        time.sleep(main_loop_sleep_interval * 5)
                        continue
                    
                    # 1. 获取当前实际持仓
                    current_exchange_positions = self.fetch_positions(self.exchange)
                    self._synchronize_status_details_with_live_positions(current_exchange_positions)
                    active_position_symbols = []
                    position_details_map = {} # {symbol: position_data}
                    if current_exchange_positions:
                        for pos in current_exchange_positions:
                            # 统一处理现货和合约的数量字段
                            qty_field = 'contracts' if pos.get('type') == 'contract' else 'amount'
                            if float(pos.get(qty_field, 0)) > 0:
                                active_position_symbols.append(pos['symbol'])
                                position_details_map[pos['symbol']] = pos
                    
                    logging.info(f"当前活跃持仓 ({len(active_position_symbols)}个): {active_position_symbols}")

                    # 2. 为现有持仓执行智能止盈止损检查
                    if position_details_map:
                        symbols_closed_by_sltp = []
                        profit_only_mode = self.config.get("profit_only_mode", True)
                        min_profit_threshold = self.config.get("min_profit_threshold", 0.003)
                        dynamic_stop_adjustment = self.config.get("dynamic_stop_adjustment", True)
                        
                        for sym, pos_data in list(position_details_map.items()):
                            try:
                                current_price = float(self.exchange.fetch_ticker(sym)['last'])
                                entry_price = pos_data.get('entry_price', 0)
                                if entry_price > 0:
                                    pnl_pct = ((current_price - entry_price) / entry_price)
                                    
                                    # 动态止损优化：结合实时监控数据
                                    dynamic_sl_pct = default_sl_pct
                                    dynamic_tp_pct = default_tp_pct
                                    
                                    if hasattr(self, 'real_time_monitor') and self.real_time_monitor and hasattr(self.real_time_monitor, 'position_history'):
                                        try:
                                            pos_history = self.real_time_monitor.position_history.get(sym)
                                            if pos_history and isinstance(pos_history, dict):
                                                risk_level = pos_history.get('risk_level', 'medium')
                                                
                                                # 根据风险等级调整止损策略
                                                if risk_level == 'high' or risk_level == 'critical':
                                                    # 高风险持仓：收紧止损，放宽止盈
                                                    dynamic_sl_pct = default_sl_pct * 0.5  # 止损更严格
                                                    dynamic_tp_pct = default_tp_pct * 0.8  # 止盈更容易触发
                                                    logging.info(f"高风险持仓 {sym}: 调整止损至 {dynamic_sl_pct*100:.1f}%, 止盈至 {dynamic_tp_pct*100:.1f}%")
                                                elif risk_level == 'low':
                                                    # 低风险持仓：放宽止损，提高止盈
                                                    dynamic_sl_pct = default_sl_pct * 1.5  # 止损更宽松
                                                    dynamic_tp_pct = default_tp_pct * 1.2  # 止盈目标更高
                                                    logging.info(f"低风险持仓 {sym}: 调整止损至 {dynamic_sl_pct*100:.1f}%, 止盈至 {dynamic_tp_pct*100:.1f}%")
                                        except Exception as e_dynamic:
                                            logging.warning(f"动态止损优化失败 {sym}: {str(e_dynamic)}")
                                    
                                    # 只许盈利不许亏损原则
                                    if profit_only_mode:
                                        # 盈利达到最小阈值时才考虑平仓
                                        if pnl_pct >= min_profit_threshold:
                                            # 动态调整止盈点
                                            if dynamic_stop_adjustment:
                                                # 根据盈利幅度动态调整止盈点
                                                if pnl_pct >= 0.02:  # 盈利2%以上，提高止盈点
                                                    tp_threshold = dynamic_tp_pct * 1.5
                                                elif pnl_pct >= 0.01:  # 盈利1%以上，正常止盈
                                                    tp_threshold = dynamic_tp_pct
                                                else:  # 小幅盈利，降低止盈点确保盈利
                                                    tp_threshold = min_profit_threshold
                                            else:
                                                tp_threshold = dynamic_tp_pct
                                            
                                            if pnl_pct >= tp_threshold:
                                                logging.info(f"智能止盈平仓: {sym}, 盈利: {pnl_pct*100:.3f}%, 阈值: {tp_threshold*100:.3f}%")
                                                self.close_position(self.exchange, sym, pos_data.get('side'))
                                                if sym in self.trade_status_details:
                                                    self.trade_status_details[sym]['status'] = 'closed_by_profit_taking'
                                                symbols_closed_by_sltp.append(sym)
                                        # 亏损时不平仓，等待回本
                                        elif pnl_pct < -dynamic_sl_pct * 2:  # 亏损过大时记录警告
                                            logging.warning(f"持仓 {sym} 亏损较大: {pnl_pct*100:.3f}%, 等待回本")
                                    else:
                                        # 传统止盈止损模式（使用动态参数）
                                        if pnl_pct >= dynamic_tp_pct or pnl_pct <= -dynamic_sl_pct:
                                            action = "止盈" if pnl_pct >= dynamic_tp_pct else "止损"
                                            logging.info(f"{action}触发平仓: {sym}, 盈亏: {pnl_pct*100:.2f}% (动态阈值: 止盈{dynamic_tp_pct*100:.1f}%, 止损{dynamic_sl_pct*100:.1f}%)")
                                            self.close_position(self.exchange, sym, pos_data.get('side'))
                                            if sym in self.trade_status_details:
                                                self.trade_status_details[sym]['status'] = 'closed_by_sl_tp'
                                            symbols_closed_by_sltp.append(sym)
                            except Exception as e_sl_check:
                                logging.error(f"执行止盈止损检查 for {sym} 失败: {str(e_sl_check)}")
                        
                        if symbols_closed_by_sltp: # 如果有平仓，重新获取持仓状态
                            current_exchange_positions = self.fetch_positions(self.exchange)
                            active_position_symbols = []
                            position_details_map = {}
                            if current_exchange_positions:
                                for pos_upd in current_exchange_positions:
                                    qty_field_upd = 'contracts' if pos_upd.get('type') == 'contract' else 'amount'
                                    if float(pos_upd.get(qty_field_upd, 0)) > 0:
                                        active_position_symbols.append(pos_upd['symbol'])
                                        position_details_map[pos_upd['symbol']] = pos_upd
                            logging.info(f"止盈止损后，更新活跃持仓 ({len(active_position_symbols)}个): {active_position_symbols}")


                    # 3. 周期性地选择新的交易品种和管理性平仓
                    new_symbols_to_consider = []
                    symbols_to_close_mgmt = []
                    if current_time_loop_start - last_symbol_selection_time >= symbol_selection_interval:
                        try:
                            # 使用智能选品策略
                            from strategy_core import execute_select_product_strategy
                            selected_symbols, strategy_name = execute_select_product_strategy(
                                exchange=self.exchange,
                                symbols=None,  # 让策略自己获取可用交易对
                                limit=5  # 最多选择5个品种
                            )
                            
                            # 转换为交易所格式并过滤已持仓品种
                            if selected_symbols:
                                # 将策略返回的符号转换为交易所格式
                                converted_symbols = []
                                for symbol in selected_symbols:
                                    if symbol.endswith('-USDT'):
                                        converted_symbol = symbol.replace('-', '/') + ':USDT'
                                    elif '/' in symbol and not symbol.endswith(':USDT'):
                                        converted_symbol = symbol + ':USDT'
                                    else:
                                        converted_symbol = symbol
                                    converted_symbols.append(converted_symbol)
                                
                                new_symbols_to_consider = [s for s in converted_symbols if s not in active_position_symbols][:3]
                                logging.info(f"智能选品策略 {strategy_name} 选出的可开仓候选: {new_symbols_to_consider}")
                            else:
                                 # 智能选品失败时的降级方案
                                 logging.warning("智能选品策略未返回有效品种，使用配置的降级候选")
                                 fallback_symbols = self.config.get("fallback_symbols", ["BTC/USDT:USDT", "ETH/USDT:USDT", "BNB/USDT:USDT"])
                                 new_symbols_to_consider = [s for s in fallback_symbols if s not in active_position_symbols][:3]
                                 logging.info(f"降级方案选出的可开仓候选: {new_symbols_to_consider}")
                                 
                                 # 记录降级事件，用于后续分析
                                 if not hasattr(self, 'fallback_count'):
                                     self.fallback_count = 0
                                 self.fallback_count += 1
                                 if self.fallback_count % 5 == 0:  # 每5次降级提醒一次
                                     logging.warning(f"智能选品已连续降级 {self.fallback_count} 次，建议检查策略配置")
                            
                            # 智能平仓触发机制 - 结合实时监控数据
                            intelligent_close_threshold = self.config.get("intelligent_close_threshold", 8)
                            if len(active_position_symbols) >= intelligent_close_threshold and position_details_map:
                                # 使用智能评分选择最差持仓进行平仓
                                try:
                                    from Select_product import select_positions_to_close
                                    
                                    # 如果有实时监控数据，优先平仓高风险持仓
                                    if self.real_time_monitor and hasattr(self.real_time_monitor, 'position_history'):
                                        high_risk_symbols = self._get_high_risk_positions()
                                        if high_risk_symbols:
                                            symbols_to_close_mgmt = high_risk_symbols[:num_to_close_on_limit]
                                            logging.info(f"基于实时监控的智能平仓，选出高风险持仓: {symbols_to_close_mgmt}")
                                        else:
                                            symbols_to_close_mgmt = select_positions_to_close(
                                                self.exchange, 
                                                position_details_map, 
                                                num_to_close=num_to_close_on_limit
                                            )
                                            logging.info(f"智能平仓触发(持仓{len(active_position_symbols)}≥{intelligent_close_threshold})，选出待平仓: {symbols_to_close_mgmt}")
                                    else:
                                        symbols_to_close_mgmt = select_positions_to_close(
                                            self.exchange, 
                                            position_details_map, 
                                            num_to_close=num_to_close_on_limit
                                        )
                                        logging.info(f"智能平仓触发(持仓{len(active_position_symbols)}≥{intelligent_close_threshold})，选出待平仓: {symbols_to_close_mgmt}")
                                except Exception as e_smart_close:
                                    logging.warning(f"智能平仓失败，使用简化平仓: {e_smart_close}")
                                    symbols_to_close_mgmt = list(position_details_map.keys())[:num_to_close_on_limit]
                                    logging.info(f"简化平仓选出待平仓: {symbols_to_close_mgmt}")
                            elif len(active_position_symbols) >= max_active_positions and position_details_map:
                                # 达到最大持仓时强制平仓
                                symbols_to_close_mgmt = list(position_details_map.keys())[:num_to_close_on_limit]
                                logging.info(f"持仓达上限({max_active_positions})，强制平仓: {symbols_to_close_mgmt}")
                            last_symbol_selection_time = current_time_loop_start
                        except Exception as e_sel:
                            logging.error(f"智能选币或管理性平仓决策失败: {str(e_sel)}")
                            last_symbol_selection_time = current_time_loop_start # 避免连续失败导致卡死
                    
                    # 4. 执行管理性平仓
                    if symbols_to_close_mgmt:
                        symbols_closed_by_mgmt = []
                        for sym_close in symbols_to_close_mgmt:
                            if sym_close in position_details_map:
                                try:
                                    self.close_position(self.exchange, sym_close, position_details_map[sym_close].get('side'))
                                    if sym_close in self.trade_status_details:
                                        self.trade_status_details[sym_close]['status'] = 'closed_by_management'
                                    symbols_closed_by_mgmt.append(sym_close)
                                except Exception as e_close_m:
                                    logging.error(f"管理性平仓 {sym_close} 失败: {str(e_close_m)}")
                        if symbols_closed_by_mgmt: # 如果有平仓，再次重新获取持仓状态
                            current_exchange_positions = self.fetch_positions(self.exchange)
                            active_position_symbols = []
                            position_details_map = {}
                            if current_exchange_positions:
                                for pos_upd_m in current_exchange_positions:
                                    qty_field_upd_m = 'contracts' if pos_upd_m.get('type') == 'contract' else 'amount'
                                    if float(pos_upd_m.get(qty_field_upd_m, 0)) > 0:
                                        active_position_symbols.append(pos_upd_m['symbol'])
                                        position_details_map[pos_upd_m['symbol']] = pos_upd_m
                            logging.info(f"管理性平仓后，更新活跃持仓 ({len(active_position_symbols)}个): {active_position_symbols}")

                    # 5. 为新选出的、未持仓的、且持仓数未达上限的品种做开仓决策
                    # new_symbols_to_consider 已经被 self.selected_symbols_for_trading 替代和管理
                    # 遍历 self.selected_symbols_for_trading 来决定是否开仓
                    symbols_opened_this_round = [] # 记录本轮成功开仓的品种，以便从待选列表中移除
                    if hasattr(self, 'selected_symbols_for_trading') and self.selected_symbols_for_trading:
                        logging.info(f"准备为智能选择的品种 {self.selected_symbols_for_trading} 进行开仓决策。")
                        for symbol_cand in self.selected_symbols_for_trading:
                            if symbol_cand in active_position_symbols: 
                                logging.info(f"{symbol_cand} 已有持仓，跳过开仓。")
                                symbols_opened_this_round.append(symbol_cand) # 标记为已处理
                                continue
                            if len(active_position_symbols) >= max_active_positions: 
                                logging.info("持仓已达上限，不再为剩余候选品种开仓。")
                                break
                            
                            try:
                                # 使用新的策略接口调用交易策略
                                try:
                                    signal_details = execute_trading_strategy(
                                        exchange=self.exchange,
                                        symbol=symbol_cand,
                                        existing_positions=current_exchange_positions,
                                        strategy_name=None  # 使用默认启用的策略
                                    )
                                    
                                    signal_action = signal_details.get('signal') # 'buy', 'sell', 'hold'
                                    order_quantity_raw = signal_details.get('quantity')
                                    decision_reason = signal_details.get('reason', 'N/A')
                                    target_price_strat = signal_details.get('price') # 策略给出的目标价，市价单时可能为None
                                    strategy_name_from_strat = signal_details.get('strategy_name', 'auto_selected')
                                except Exception as e:
                                    logging.error(f"调用交易策略失败: {str(e)}，使用保守策略")
                                    signal_details = {
                                        'signal': 'hold',
                                        'quantity': 0,
                                        'reason': f'策略调用失败: {str(e)}',
                                        'price': None,
                                        'strategy_name': 'error_fallback'
                                    }
                                    signal_action = 'hold'
                                    order_quantity_raw = 0
                                    decision_reason = signal_details['reason']
                                    target_price_strat = None
                                    strategy_name_from_strat = 'error_fallback'

                                logging.info(f"为 {symbol_cand} 获取到策略信号: {signal_action}, 数量: {order_quantity_raw}, 价格: {target_price_strat}, 原因: {decision_reason}, 策略名: {strategy_name_from_strat}")

                                # 智能开仓优化：结合实时监控数据进行开仓决策
                                should_open_position = True
                                optimization_reason = ""
                                
                                if signal_action == 'buy' and hasattr(self, 'real_time_monitor') and self.real_time_monitor:
                                    try:
                                        # 获取市场整体风险评估
                                        market_risk = self.real_time_monitor._assess_market_risk()
                                        
                                        # 检查当前持仓的整体表现
                                        if hasattr(self.real_time_monitor, 'position_history') and self.real_time_monitor.position_history:
                                            recent_positions = list(self.real_time_monitor.position_history.values())[-10:]  # 最近10个持仓
                                            if recent_positions:
                                                avg_performance = sum(pos.get('current_pnl_percent', 0) for pos in recent_positions) / len(recent_positions)
                                                
                                                # 如果最近持仓表现不佳且市场风险较高，降低开仓积极性
                                                if avg_performance < -2.0 and market_risk == 'high':
                                                    should_open_position = False
                                                    optimization_reason = f"市场风险高且近期持仓表现不佳(平均{avg_performance:.2f}%)，暂缓开仓"
                                                elif avg_performance < -5.0:
                                                    should_open_position = False
                                                    optimization_reason = f"近期持仓表现严重不佳(平均{avg_performance:.2f}%)，暂停开仓"
                                        
                                        # 检查是否有相关性过高的持仓
                                        if should_open_position and hasattr(self.real_time_monitor, 'position_history'):
                                            similar_positions = 0
                                            for pos_symbol, pos_data in self.real_time_monitor.position_history.items():
                                                if pos_data.get('status') == 'active':
                                                    # 简单的相关性检查（基于交易对前缀）
                                                    if symbol_cand.split('/')[0] == pos_symbol.split('/')[0] or symbol_cand.split('/')[1] == pos_symbol.split('/')[1]:
                                                        similar_positions += 1
                                            
                                            if similar_positions >= 2:
                                                should_open_position = False
                                                optimization_reason = f"已有{similar_positions}个相关持仓，避免过度集中风险"
                                        
                                        if not should_open_position:
                                            logging.info(f"智能开仓优化: {symbol_cand} - {optimization_reason}")
                                    
                                    except Exception as e_optimization:
                                        logging.warning(f"智能开仓优化检查失败: {str(e_optimization)}，继续正常开仓流程")
                                        should_open_position = True

                                if signal_action == 'buy' and should_open_position:
                                    if order_quantity_raw is None or float(order_quantity_raw) <= 0:
                                        logging.warning(f"{symbol_cand} 策略信号为买入但数量无效 ({order_quantity_raw})，跳过。")
                                        symbols_opened_this_round.append(symbol_cand) # 标记为已处理（跳过）
                                        continue

                                    quantity_final = float(self.exchange.amount_to_precision(symbol_cand, order_quantity_raw))
                                    if quantity_final <= 0:
                                        logging.warning(f"精度处理后 {symbol_cand} 订单数量为零或负 ({quantity_final})，跳过开仓。")
                                        symbols_opened_this_round.append(symbol_cand) # 标记为已处理（跳过）
                                        continue

                                    logging.info(f"计划为 {symbol_cand} 开仓，信号: {signal_action}, 原始数量: {order_quantity_raw}, 计算后数量: {quantity_final}")
                                    
                                    o_params_strat = {'tdMode': self.config.get("margin_mode", "cross")}
                                    pos_side_strat = 'long' # 买入开多
                                    if ":" in symbol_cand: # 合约交易
                                        o_params_strat['posSide'] = pos_side_strat
                                        leverage_setting_strat = self.config.get("leverage_map", {}).get(symbol_cand, self.config.get("default_leverage", 1))
                                        if leverage_setting_strat:
                                            try:
                                                # 确保杠杆设置正确传递参数
                                                self.exchange.set_leverage(leverage_setting_strat, symbol_cand, {'mgnMode': o_params_strat['tdMode'], 'posSide': pos_side_strat if o_params_strat['tdMode'] != 'isolated' else None})
                                                logging.info(f"为 {symbol_cand} (posSide: {pos_side_strat}) 设置杠杆 {leverage_setting_strat}x 成功。")
                                            except Exception as e_set_lev_strat:
                                                logging.warning(f"为 {symbol_cand} (posSide: {pos_side_strat}) 设置杠杆 {leverage_setting_strat}x 失败: {e_set_lev_strat}")

                                    order_res_strat = self.create_order(self.exchange, symbol_cand, 'market', signal_action, quantity_final, None, o_params_strat)
                                    
                                    if order_res_strat and order_res_strat.get('id'):
                                        current_entry_time_strat = time.time()
                                        # 获取开仓时的价格用于记录，如果订单信息不包含，则尝试获取市价
                                        entry_price_for_status = order_res_strat.get('price')
                                        if not entry_price_for_status or entry_price_for_status == 0:
                                            try: entry_price_for_status = self.exchange.fetch_ticker(symbol_cand)['last']
                                            except: entry_price_for_status = 0 # 获取失败则为0
                                        
                                        # 获取已保存的选择策略，如果不存在则设为 'N/A'
                                        selection_strategy_name = self.trade_status_details.get(symbol_cand, {}).get('selection_strategy', 'N/A')

                                        self.trade_status_details[symbol_cand].update({
                                            'status': 'opening', 
                                            'side': signal_action, 
                                            'order_id': order_res_strat.get('id'),
                                            'quantity': order_res_strat.get('amount', quantity_final),
                                            'entry_price': entry_price_for_status,
                                            'entry_time': current_entry_time_strat, 
                                            'last_update': time.time(),
                                            'position_side': pos_side_strat if ":" in symbol_cand else None,
                                            'type': "合约" if ":" in symbol_cand else "现货",
                                            'selection_strategy': selection_strategy_name, # 保存选择策略
                                            'trading_strategy': strategy_name_from_strat, # 保存交易策略
                                            'decision_reason': decision_reason
                                        })
                                        logging.info(f"TRADE_LOOP (SmartStrat): Set entry_time for {symbol_cand} to {current_entry_time_strat}")
                                        active_position_symbols.append(symbol_cand)
                                        symbols_opened_this_round.append(symbol_cand) # 标记为已成功开仓
                                        logging.info(f"为 {symbol_cand} (策略: {strategy_name_from_strat}) 提交开仓订单成功, ID: {order_res_strat.get('id')}")
                                    else:
                                        logging.error(f"为 {symbol_cand} (策略: {strategy_name_from_strat}) 提交开仓订单失败: {order_res_strat}")
                                        symbols_opened_this_round.append(symbol_cand) # 标记为已处理（失败）
                                elif signal_action == 'buy' and not should_open_position:
                                    # 智能开仓优化拒绝了开仓
                                    logging.info(f"智能开仓优化拒绝开仓 {symbol_cand}: {optimization_reason}")
                                    symbols_opened_this_round.append(symbol_cand) # 标记为已处理
                                elif signal_action == 'sell':
                                    logging.info(f"策略信号为卖出 {symbol_cand} (原因: {decision_reason})。当前主要逻辑为开多仓或平仓（平仓在其他地方处理）。跳过主动开空仓。")
                                    symbols_opened_this_round.append(symbol_cand) # 标记为已处理
                                else: # 'hold' or other signals
                                    logging.info(f"策略信号为 {signal_action} for {symbol_cand} (原因: {decision_reason})，不执行开仓操作。")
                                    symbols_opened_this_round.append(symbol_cand) # 标记为已处理

                            except Exception as e_strat_open:
                                logging.error(f"为 {symbol_cand} 执行智能策略开仓决策或下单失败: {str(e_strat_open)}")
                                logging.error(traceback.format_exc())
                                symbols_opened_this_round.append(symbol_cand) # 标记为已处理（异常）
                    
                    # 清理已处理的品种 (无论成功与否)
                    if hasattr(self, 'selected_symbols_for_trading') and self.selected_symbols_for_trading and symbols_opened_this_round:
                        self.selected_symbols_for_trading = [s for s in self.selected_symbols_for_trading if s not in symbols_opened_this_round]
                        if not self.selected_symbols_for_trading:
                            logging.info("所有本轮智能选择的品种均已处理完毕。")
                        else:
                            logging.info(f"剩余待处理的智能选择品种: {self.selected_symbols_for_trading}")

                    
                    # 6. 性能统计和报告
                    if hasattr(self, 'real_time_monitor') and self.real_time_monitor:
                        try:
                            # 每20个循环周期生成一次性能报告，减少频繁统计
                            if not hasattr(self, '_performance_report_counter'):
                                self._performance_report_counter = 0
                            self._performance_report_counter += 1
                            
                            if self._performance_report_counter >= 20:
                                self._generate_performance_report()
                                self._performance_report_counter = 0
                        except Exception as e_perf_report:
                            logging.error(f"生成性能报告失败: {e_perf_report}")
                    
                    # 7. 更新UI (通过 self.main_root.after 来保证线程安全)
                    if hasattr(self, 'main_root') and self.main_root and hasattr(self.main_root, 'after'):
                        try:
                            # 直接调用after方法更新UI，避免使用自定义队列机制
                            self.main_root.after(0, self.update_main_trading_table)
                        except Exception as e_ui_schedule:
                            logging.error(f"调度UI更新失败: {e_ui_schedule}")
                            # 尝试直接更新
                            try:
                                self.update_main_trading_table()
                            except Exception as e_direct_update:
                                logging.error(f"直接更新UI失败: {e_direct_update}")
                    elif hasattr(self, 'main_status_var') and self.main_status_var: # Fallback to status bar if full UI update fails to schedule
                        self.main_status_var.set("自动交易中...")

                except Exception as e_loop:
                    # 增强错误处理机制 - 错误分类和恢复策略
                    error_type = type(e_loop).__name__
                    error_msg = str(e_loop)
                    
                    # 初始化错误计数器
                    if not hasattr(self, '_loop_error_count'):
                        self._loop_error_count = 0
                        self._last_error_time = 0
                    
                    self._loop_error_count += 1
                    current_error_time = time.time()
                    
                    # 错误频率检测
                    if current_error_time - self._last_error_time < 60:  # 1分钟内连续错误
                        error_frequency = "高频"
                        main_loop_sleep_interval = min(main_loop_sleep_interval * 2, 30)  # 增加休眠时间
                    else:
                        error_frequency = "正常"
                        self._loop_error_count = 1  # 重置计数
                    
                    # 分类错误处理
                    if "network" in error_msg.lower() or "connection" in error_msg.lower():
                        logging.warning(f"网络错误({error_frequency}频率,第{self._loop_error_count}次): {error_msg}")
                        if hasattr(self, 'main_status_var') and self.main_status_var: 
                            self.main_status_var.set(f"网络异常,正在重试...")
                    elif "api" in error_msg.lower() or "rate" in error_msg.lower():
                        logging.warning(f"API限制错误({error_frequency}频率,第{self._loop_error_count}次): {error_msg}")
                        if hasattr(self, 'main_status_var') and self.main_status_var: 
                            self.main_status_var.set(f"API限制,延长间隔...")
                    else:
                        logging.error(f"主循环未知错误({error_frequency}频率,第{self._loop_error_count}次): {error_type} - {error_msg}")
                        if self._loop_error_count <= 3:  # 只在前3次记录完整堆栈
                            logging.error(traceback.format_exc())
                        if hasattr(self, 'main_status_var') and self.main_status_var: 
                            self.main_status_var.set(f"系统错误: {error_msg[:30]}...")
                    
                    self._last_error_time = current_error_time
                    
                    # 错误恢复策略
                    if self._loop_error_count > 10:
                        logging.critical(f"主循环连续错误超过10次，系统可能存在严重问题")
                        if hasattr(self, 'main_status_var') and self.main_status_var: 
                            self.main_status_var.set(f"严重错误,请检查日志")
                
                # 内存监控和垃圾回收机制
                if not hasattr(self, '_memory_check_counter'):
                    self._memory_check_counter = 0
                
                self._memory_check_counter += 1
                
                # 每50个循环周期进行内存检查和垃圾回收
                if self._memory_check_counter % 50 == 0:
                    try:
                        import psutil
                        import gc
                        
                        # 获取当前进程内存使用情况
                        process = psutil.Process()
                        memory_info = process.memory_info()
                        memory_mb = memory_info.rss / 1024 / 1024
                        
                        # 记录内存使用情况
                        logging.info(f"内存使用: {memory_mb:.1f}MB, 循环计数: {self._memory_check_counter}")
                        
                        # 如果内存使用超过500MB，执行垃圾回收
                        if memory_mb > 500:
                            logging.warning(f"内存使用过高({memory_mb:.1f}MB)，执行垃圾回收")
                            collected = gc.collect()
                            logging.info(f"垃圾回收完成，回收对象数: {collected}")
                            
                            # 清理缓存数据
                            if hasattr(self, '_price_cache') and len(self._price_cache) > 100:
                                # 保留最近的50个价格缓存
                                cache_items = list(self._price_cache.items())
                                self._price_cache = dict(cache_items[-50:])
                                logging.info("清理价格缓存，保留最近50个")
                            
                            # 清理持仓历史数据
                            if hasattr(self, 'position_history') and len(self.position_history) > 1000:
                                self.position_history = self.position_history[-500:]
                                logging.info("清理持仓历史，保留最近500条")
                    
                    except ImportError:
                        # psutil未安装时的简化版本
                        import gc
                        collected = gc.collect()
                        if collected > 0:
                            logging.info(f"执行垃圾回收，回收对象数: {collected}")
                    except Exception as e_memory:
                        logging.warning(f"内存监控失败: {e_memory}")
                
                time.sleep(main_loop_sleep_interval)
            
            logging.info("自动交易循环线程已结束。")
            if hasattr(self, 'main_status_var') and self.main_status_var: self.main_status_var.set("已停止 (循环结束)")

        self.trade_thread = threading.Thread(target=trade_loop, daemon=True, name="MainTrading")
        self._register_thread(self.trade_thread)
        self.trade_thread.start()
        logging.info("auto_trading 方法执行完毕，交易线程已启动。")

    # 已删除冗余的start_formal_trading_after_test和start_high_frequency_selection函数
    # 现在直接通过激活现有的auto_trading循环来实现正式模拟交易

    # 已删除execute_new_position_decisions函数，现在使用auto_trading循环中的开仓逻辑

    def update_main_trading_table(self):
        """
        更新主交易窗口的表格和资产信息。
        直接API数据获取 + 优化显示方案
        """
        if not hasattr(self, 'main_root') or not self.main_root.winfo_exists():
            logging.warning("UI更新中止：主窗口不存在。")
            return

        try:
            # 初始化存储所有行数据的列表
            all_rows_data = []
            # 1. 直接获取实时数据
            current_time = time.time()
            
            # 更新资产信息
            if hasattr(self, 'asset_label') and self.asset_label:
                try:
                    balance_info = self.exchange.fetch_balance()
                    asset_value = balance_info['total'].get('USDT', 0)
                    self.asset_label.config(text=f"账户资产总值: {asset_value:.2f} USDT", 
                                           fg="red" if asset_value == 0 else "black")
                except Exception as e_asset:
                    logging.error(f"更新资产标签失败: {str(e_asset)}")
                    self.asset_label.config(text="账户资产总值: 获取失败", fg="red")
            
            # 2. 智能持仓数据获取 - 高级缓存机制
            if not hasattr(self, '_positions_cache'):
                self._positions_cache = {}
                self._positions_cache_time = 0
                self._positions_fetch_errors = 0
            
            # 动态缓存时间：8-60秒，根据错误次数调整（性能优化版本）
            positions_cache_duration = min(60, 8 + self._positions_fetch_errors * 4)
            
            if current_time - self._positions_cache_time > positions_cache_duration:
                try:
                    current_positions_list = self.exchange.fetch_positions()
                    live_positions_map = {}
                    active_positions_count = 0
                    
                    if current_positions_list:
                        for pos in current_positions_list:
                            contracts = float(pos.get('contracts', 0))
                            amount = float(pos.get('amount', 0))
                            if contracts > 0 or amount > 0:
                                live_positions_map[pos['symbol']] = pos
                                active_positions_count += 1
                                # 只在持仓变化时记录日志，减少日志噪音
                                if pos['symbol'] not in self._positions_cache:
                                    logging.info(f"新增活跃持仓: {pos['symbol']} - {contracts or amount}")
                    
                    # 检测持仓变化
                    if len(live_positions_map) != len(self._positions_cache):
                        logging.info(f"持仓数量变化: {len(self._positions_cache)} -> {len(live_positions_map)}")
                    
                    # 更新缓存
                    self._positions_cache = live_positions_map
                    self._positions_cache_time = current_time
                    self._positions_fetch_errors = 0
                    
                except Exception as e_pos:
                    self._positions_fetch_errors += 1
                    if self._positions_fetch_errors <= 3:
                        logging.error(f"获取持仓数据失败(第{self._positions_fetch_errors}次): {str(e_pos)}")
                    
                    # 使用缓存数据
                    live_positions_map = self._positions_cache.copy() if self._positions_cache else {}
            else:
                # 使用缓存数据
                live_positions_map = self._positions_cache.copy() if self._positions_cache else {}
            
            # 合并 self.trade_status_details 和 live_positions_map 的键，作为要显示的品种
            # 优先显示实际持仓，确保所有活跃持仓都能正确显示
            symbols_to_display = list(live_positions_map.keys())
            for sym in self.trade_status_details.keys():
                if sym not in symbols_to_display:
                    symbols_to_display.append(sym)
            
            # 记录显示状态用于调试
            logging.info(f"UI_UPDATE: 实际持仓数量: {len(live_positions_map)}, 程序状态数量: {len(self.trade_status_details)}, 总显示数量: {len(symbols_to_display)}")
            if len(live_positions_map) > 0:
                logging.info(f"UI_UPDATE: 实际持仓品种: {list(live_positions_map.keys())}")
            if len(symbols_to_display) > 12:
                logging.warning(f"UI_UPDATE: 显示品种数量({len(symbols_to_display)})超过限制(12)，将只显示前12个")

            # 获取账户总余额，用于计算占用资金比例
            # 确保属性存在，防止AttributeError
            if not hasattr(self, '_cached_total_balance_usdt'):
                self._cached_total_balance_usdt = 0
            if not hasattr(self, '_last_balance_fetch_time'):
                self._last_balance_fetch_time = 0
                
            total_account_balance_usdt = self._cached_total_balance_usdt # 默认使用缓存值
            current_time = time.time()
            # 智能余额获取策略 - 动态缓存时间调整
            if not hasattr(self, '_balance_fetch_errors'):
                self._balance_fetch_errors = 0
            
            # 智能动态缓存策略：基础60秒，错误时指数增长，最大5分钟
            base_cache_duration = 60  # 基础缓存时长
            error_multiplier = min(8, self._balance_fetch_errors * 2)  # 错误倍数，最大8倍
            balance_cache_duration = min(300, base_cache_duration + error_multiplier * 15)  # 最大5分钟
            
            if current_time - self._last_balance_fetch_time > balance_cache_duration or self._cached_total_balance_usdt == 0:
                try:
                    # 更新账户余额（智能频率控制）
                    balance_info_for_table = self.exchange.fetch_balance()
                    total_account_balance_usdt = balance_info_for_table['total'].get('USDT', 0)
                    self._cached_total_balance_usdt = total_account_balance_usdt
                    self._last_balance_fetch_time = current_time
                    
                    # 成功时逐步减少错误计数（渐进恢复）
                    if self._balance_fetch_errors > 0:
                        self._balance_fetch_errors = max(0, self._balance_fetch_errors - 1)
                    
                    # 每5分钟记录一次成功日志（减少日志噪音）
                    if not hasattr(self, '_last_balance_success_log'):
                        self._last_balance_success_log = 0
                    if current_time - self._last_balance_success_log > 300:
                        self._last_balance_success_log = current_time
                        logging.info(f"账户余额更新: {total_account_balance_usdt:.2f} USDT (缓存{balance_cache_duration}s)")
                    
                except Exception as e_bal_ui:
                    # 智能错误处理和恢复机制
                    self._balance_fetch_errors += 1
                    if not hasattr(self, '_cached_total_balance_usdt'):
                        self._cached_total_balance_usdt = 0
                    
                    # 智能日志策略：前3次详细记录，之后每10次记录一次
                    should_log = (self._balance_fetch_errors <= 3 or 
                                 self._balance_fetch_errors % 10 == 0)
                    if should_log:
                        error_msg = str(e_bal_ui)[:100] + ('...' if len(str(e_bal_ui)) > 100 else '')
                        logging.warning(f"余额获取失败(第{self._balance_fetch_errors}次，缓存{balance_cache_duration}s): {error_msg}")
                    
                    total_account_balance_usdt = self._cached_total_balance_usdt # 使用缓存值
            # 使用缓存余额（减少日志输出）

            # 动态计算需要显示的行数，确保所有交易品种都能显示
            actual_symbols_count = len(symbols_to_display)
            # 总行数 = 建仓行数 + 平仓行数，确保平仓行始终显示
            needed_rows = max(self.display_rows_limit + self.close_rows_limit, actual_symbols_count + self.close_rows_limit)
            
            # 如果需要更多行，动态扩展表格
            current_rows = len(self.positions_tree.get_children())
            if needed_rows > current_rows:
                for i in range(current_rows, needed_rows):
                    # 保持与初始化时一致的序号格式
                    if i < self.display_rows_limit:
                        seq_display = f"交易{i+1}"
                    else:
                        seq_display = f"平仓{i-self.display_rows_limit+1}"
                    val = [seq_display, "待机", ""] + ["" for _ in range(len(self.positions_tree["columns"]) - 1)]
                    self.positions_tree.insert("", "end", values=val)
                logging.info(f"UI_UPDATE: 动态扩展表格行数从 {current_rows} 到 {needed_rows}")
            
            for i in range(needed_rows):
                # 序号列的显示，前两行固定为“测试X”，之后从3开始，或者直接从1开始递增
                # 根据最新需求，前两行是测试，后续是实际交易，序号应该能区分
                # 但主表本身序号从1开始即可，具体是否为测试由策略名等其他列体现
                # 保持与初始化时一致的序号格式
                if i < self.display_rows_limit:
                    seq_num_display = f"交易{i+1}"
                else:
                    seq_num_display = f"平仓{i-self.display_rows_limit+1}"
                # if i < 2:
                #     seq_num_display = f"测试{i + 1}"
                # else:
                #     # 实际交易的序号，如果symbols_to_display[i-2]存在，则用i-2+1，否则用i+1
                #     # 这个逻辑比较复杂且可能出错，直接用 i+1 简化，通过其他列区分
                #     seq_num_display = f"{i + 1}"

                row_values = [seq_num_display] + [""] * (len(self.positions_tree["columns"]) - 1)
                
                if i < len(symbols_to_display):
                    symbol = symbols_to_display[i]
                    try:
                        status_detail = self.trade_status_details.get(symbol, {})
                        position_data = live_positions_map.get(symbol)
                        
                        # 简化策略名称显示逻辑
                        strategy_name_display = status_detail.get('strategy_name', '待机')
                        
                        if not strategy_name_display or strategy_name_display == '':
                            strategy_name_display = '待机'
                        
                        symbol_type = status_detail.get('type', "合约" if ":" in symbol else "现货")
                        current_price_str = ""
                        
                        # 高级价格缓存机制 - 智能API调用频率控制
                        price_cache_key = f"price_{symbol}"
                        if not hasattr(self, '_price_cache'):
                            self._price_cache = {}
                            self._price_cache_time = {}
                            self._price_fetch_errors = {}  # 错误计数器
                            self._batch_price_queue = set()  # 批量获取队列
                        
                        # 动态调整缓存时间：错误越多，缓存时间越长（性能优化版本）
                        error_count = self._price_fetch_errors.get(symbol, 0)
                        min_duration = self.perf_config.get('cache_settings', {}).get('price_cache_duration_min', 10)  # 提升最小缓存时间
                        max_duration = self.perf_config.get('cache_settings', {}).get('price_cache_duration_max', 60)  # 提升最大缓存时间
                        cache_duration = min(max_duration, min_duration + error_count * 3)  # 增加错误惩罚
                        
                        # 智能价格获取策略 - 异步优化版本
                        if (price_cache_key not in self._price_cache_time or 
                            current_time - self._price_cache_time[price_cache_key] > cache_duration):
                            
                            # 添加到批量获取队列
                            self._batch_price_queue.add(symbol)
                            
                            # 当队列达到一定数量或超时时，异步批量获取（性能优化版本）
                            batch_threshold = self.perf_config.get('api_limits', {}).get('batch_fetch_threshold', 5)  # 提升批量阈值
                            batch_timeout = self.perf_config.get('api_limits', {}).get('batch_fetch_timeout', 15)  # 增加批量超时
                            if len(self._batch_price_queue) >= batch_threshold or not hasattr(self, '_last_batch_fetch') or current_time - self._last_batch_fetch > batch_timeout:
                                try:
                                    # 使用异步方法批量获取价格
                                    symbols_to_fetch = list(self._batch_price_queue)
                                    fetched_prices = self._async_fetch_data(symbols_to_fetch)
                                    
                                    # 处理获取到的价格
                                    for sym, price in fetched_prices.items():
                                        cache_key = f"price_{sym}"
                                        self._price_cache[cache_key] = price
                                        self._price_cache_time[cache_key] = current_time
                                        self._price_fetch_errors[sym] = 0  # 重置错误计数
                                    
                                    # 对于未成功获取的symbol，使用传统方法作为备用（限制频率）
                                    failed_symbols = set(symbols_to_fetch) - set(fetched_prices.keys())
                                    if failed_symbols and len(failed_symbols) <= 3:  # 限制单次备用获取数量
                                        for sym in list(failed_symbols)[:3]:  # 最多处理3个失败的symbol
                                            try:
                                                # 检查该symbol的最后获取时间，避免频繁重试
                                                last_attempt_key = f"last_attempt_{sym}"
                                                last_attempt_time = getattr(self, '_price_last_attempt', {}).get(last_attempt_key, 0)
                                                if current_time - last_attempt_time < 10:  # 10秒内不重试
                                                    continue
                                                
                                                if not hasattr(self, '_price_last_attempt'):
                                                    self._price_last_attempt = {}
                                                self._price_last_attempt[last_attempt_key] = current_time
                                                
                                                ticker = self.exchange.fetch_ticker(sym)
                                                price = float(ticker['last'])
                                                cache_key = f"price_{sym}"
                                                self._price_cache[cache_key] = price
                                                self._price_cache_time[cache_key] = current_time
                                                self._price_fetch_errors[sym] = 0
                                                logging.debug(f"备用方法成功获取{sym}价格: {price}")
                                            except Exception as e_backup:
                                                self._price_fetch_errors[sym] = self._price_fetch_errors.get(sym, 0) + 1
                                                if self._price_fetch_errors[sym] <= 2:
                                                    logging.warning(f"备用方法获取{sym}价格失败: {e_backup}")
                                    
                                    self._batch_price_queue.clear()
                                    self._last_batch_fetch = current_time
                                    
                                except Exception as e_price:
                                    # 智能错误处理
                                    self._price_fetch_errors[symbol] = self._price_fetch_errors.get(symbol, 0) + 1
                                    if self._price_fetch_errors[symbol] <= 3:  # 前3次错误记录日志
                                        logging.warning(f"获取{symbol}价格失败(第{self._price_fetch_errors[symbol]}次): {e_price}")
                                    self._batch_price_queue.discard(symbol)
        
                        # 增强价格显示 - 智能精度和状态提示
                        if price_cache_key in self._price_cache:
                            cached_price = self._price_cache[price_cache_key]
                            cache_age = current_time - self._price_cache_time.get(price_cache_key, 0)
                            
                            # 根据价格大小动态调整显示精度
                            if cached_price >= 1000:
                                price_format = f"{cached_price:.2f}"  # 大价格保留2位小数
                            elif cached_price >= 1:
                                price_format = f"{cached_price:.4f}"  # 中等价格保留4位小数
                            else:
                                price_format = f"{cached_price:.6f}"  # 小价格保留6位小数
                            
                            # 缓存状态提示优化
                            if cache_age > 120:  # 超过2分钟显示警告
                                current_price_str = f"{price_format} ⚠️{int(cache_age)}s"
                            elif cache_age > 60:  # 超过1分钟显示缓存标识
                                current_price_str = f"{price_format} 📦{int(cache_age)}s"
                            elif cache_age > 30:  # 超过30秒显示轻微提示
                                current_price_str = f"{price_format} ⏱{int(cache_age)}s"
                            else:
                                current_price_str = f"{price_format} ✅"  # 实时数据标识
                        else:
                            current_price_str = "🔄 获取中..."

                        # 基础数据填充 - 在具体处理前先填充通用字段
                        row_values[1] = "待机"  # 状态列 (索引1) - 默认状态，后续会被覆盖
                        row_values[2] = symbol_type  # 类型列 (索引2)
                        row_values[5] = symbol  # 交易品种列 (索引5)
                        row_values[6] = current_price_str  # 最新价格列 (索引6)

                        try:
                            if position_data: # 有实际持仓
                                entry_price = float(position_data.get('entryPrice', 0))
                                qty_field = 'contracts' if position_data.get('type') == 'contract' else 'amount'
                                quantity = float(position_data.get(qty_field, 0))
                                # 获取持仓数据（减少日志输出）
                                side = position_data.get('side', status_detail.get('side', ''))
                                # 增强杠杆倍数获取 - 多数据源优先级
                                leverage = 1.0
                                leverage_source = "默认"
                                
                                if symbol_type == "合约":
                                    # 第一优先级：trade_status_details中的杠杆数据
                                    if status_detail.get('leverage') is not None:
                                        leverage = float(status_detail.get('leverage'))
                                        leverage_source = "API记录"
                                    # 第二优先级：实时持仓数据中的杠杆
                                    elif position_data.get('leverage') is not None:
                                        leverage = float(position_data.get('leverage', 1))
                                        leverage_source = "实时API"
                                    # 第三优先级：配置文件默认值
                                    else:
                                        leverage = float(getattr(self, 'default_leverage', 10))
                                        leverage_source = "配置默认"
                                    
                                    # 调试日志记录杠杆来源
                                    logging.debug(f"UI_LEVERAGE: {symbol} 杠杆 {leverage:.0f}x 来源: {leverage_source}")
                                else:
                                    leverage = 1.0  # 现货固定1倍
                            
                                # 增强盈亏计算 - 优先使用API实时数据
                                pnl, pnl_pct = 0, 0
                                pnl_source = "计算"
                                
                                try:
                                    # 第一优先级：使用API实时盈亏数据
                                    if status_detail.get('unrealized_pnl') is not None:
                                        pnl = float(status_detail.get('unrealized_pnl'))
                                        pnl_source = "API实时"
                                        
                                        # 如果有API实时收益率，直接使用
                                        if status_detail.get('unrealized_pnl_pct') is not None:
                                            pnl_pct = float(status_detail.get('unrealized_pnl_pct'))
                                        else:
                                            # 根据实时盈亏计算收益率
                                            if entry_price and entry_price > 0:
                                                pnl_pct = (pnl / (entry_price * quantity)) * 100
                                    
                                    # 第二优先级：从实时持仓数据获取
                                    elif position_data.get('unrealizedPnl') is not None:
                                        pnl = float(position_data.get('unrealizedPnl', 0))
                                        pnl_source = "持仓API"
                                        
                                        if position_data.get('percentage') is not None:
                                            pnl_pct = float(position_data.get('percentage', 0))
                                        else:
                                            if entry_price and entry_price > 0:
                                                pnl_pct = (pnl / (entry_price * quantity)) * 100
                                    
                                    # 第三优先级：传统计算方法
                                    else:
                                        pnl, pnl_pct = self.calculate_position_pnl(self.exchange, position_data)
                                        pnl_source = "传统计算"
                                    
                                    # 调试日志记录盈亏来源
                                    if abs(pnl) > 0.01:  # 只记录有意义的盈亏
                                        logging.debug(f"UI_PNL: {symbol} 盈亏 {pnl:.4f} ({pnl_pct:.2f}%) 来源: {pnl_source}")
                                        
                                except Exception as e_pnl_calc:
                                    logging.error(f"Error calculating PNL for {symbol} in UI: {e_pnl_calc}")
                                    pnl, pnl_pct = 0, 0  # 保持默认0

                                # 增强保证金计算 - 多数据源优先级
                                margin = 0.0
                                margin_source = "计算"
                                
                                # 第一优先级：API实际保证金数据
                                if status_detail.get('initial_margin') is not None:
                                    margin = float(status_detail.get('initial_margin'))
                                    margin_source = "API记录"
                                elif position_data.get('initialMargin') is not None:
                                    margin = float(position_data.get('initialMargin', 0))
                                    margin_source = "实时API"
                                # 第二优先级：根据交易类型计算
                                elif symbol_type == "现货":
                                    margin = entry_price * quantity  # 现货的占用资金是成本
                                    margin_source = "现货成本"
                                elif symbol_type == "合约" and entry_price and quantity and leverage:
                                    margin = (entry_price * quantity) / leverage  # 合约保证金
                                    margin_source = "合约计算"
                                
                                # 计算资金占比
                                margin_ratio_str = "0.00%"
                                if total_account_balance_usdt > 0 and margin > 0:
                                    margin_ratio = (margin / total_account_balance_usdt) * 100
                                    margin_ratio_str = f"{margin_ratio:.2f}%"
                                
                                # 调试日志记录保证金来源
                                if margin > 0.01:
                                    logging.debug(f"UI_MARGIN: {symbol} 保证金 {margin:.4f} ({margin_ratio_str}) 来源: {margin_source}")
                            
                                # 严格使用 status_detail 中记录的 entry_time (秒级时间戳)
                                entry_time_from_status = status_detail.get('entry_time', 0)
                                entry_time_ts = entry_time_from_status # 默认使用 status_detail 的值
                                source_of_entry_time = "status_detail"

                                if entry_time_from_status == 0: # 仅当 status_detail.entry_time 不存在或为0时
                                    if position_data:
                                        raw_pos_ts = position_data.get('timestamp', 0)
                                        logging.warning(f"UI_TABLE: Symbol {symbol}: 'entry_time' is 0 in status_detail. Falling back to position_data['timestamp'] ({raw_pos_ts}).")
                                        if raw_pos_ts > 100000000000: # 毫秒
                                            entry_time_ts = raw_pos_ts / 1000
                                            source_of_entry_time = "position_data.timestamp (ms)"
                                        elif raw_pos_ts > 0: # 秒
                                            entry_time_ts = raw_pos_ts
                                            source_of_entry_time = "position_data.timestamp (s)"
                                        else: # raw_pos_ts is 0 or invalid
                                             source_of_entry_time = "status_detail (still 0, no valid fallback)"
                                    else:
                                        logging.warning(f"UI_TABLE: Symbol {symbol}: 'entry_time' is 0 in status_detail and no position_data available.")
                                        source_of_entry_time = "status_detail (0, no position_data)"
                            
                                # 计算持仓时间（减少日志输出）
                                entry_time_str = ""
                                holding_time_str = ""
                                if entry_time_ts and entry_time_ts > 0: # 确保 entry_time_ts 是有效的秒级时间戳
                                    try:
                                        # Handle ms (from exchange) and s (from time.time()) timestamps
                                        entry_time_s = entry_time_ts / 1000 if entry_time_ts > 10000000000 else entry_time_ts
                                        entry_time_str = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(entry_time_s))
                                        current_time_for_holding = time.time()
                                        seconds_passed = current_time_for_holding - entry_time_s
                                        minutes_passed = int(seconds_passed / 60)
                                        holding_time_str = f"{max(1, minutes_passed)}分钟"
                                    except Exception as e_hold_time:
                                        logging.error(f"Error calculating holding time for {symbol}: {e_hold_time}")
                                        holding_time_str = "错误"
                                        pass
                                # 策略名称的显示逻辑
                                # self.positions_tree["columns"] = ('#0', '类型', '交易品种', '最新价格', '状态', '建仓价格', '建仓时间', '平仓价格', '平仓时间', '持仓时间(分)', '方向', '选股策略', '交易策略', '数量', '倍数', '手续费', '盈亏额', '收益率%', '占用资金', '资金占比', '账户总额')
                                selection_strategy = status_detail.get('selection_strategy', 'N/A')
                                trading_strategy = status_detail.get('trading_strategy', 'N/A')

                                # 修复列索引映射 - 基于实际列定义: ["序号", "状态", "类型", "选择策略", "交易策略", "交易品种", "最新价格", "建仓价格", "建仓时间", "平仓价格", "平仓时间", "持仓时间", "方向 (多/空)", "数量", "倍数", "手续费", "盈亏", "收益率 %", "占用账户资金", "占用账户资金比例", "账户余额"]
                                row_values[1] = "持仓中"  # 状态列 (索引1)
                                row_values[2] = symbol_type  # 类型列 (索引2)
                                row_values[3] = selection_strategy  # 选择策略列 (索引3)
                                row_values[4] = trading_strategy  # 交易策略列 (索引4)
                                row_values[5] = symbol  # 交易品种列 (索引5)
                                row_values[6] = current_price_str  # 最新价格列 (索引6)
                                row_values[7] = f"{entry_price:.4f}" if entry_price else ""  # 建仓价格列 (索引7)
                                row_values[8] = entry_time_str  # 建仓时间列 (索引8)
                                # 平仓价格[9], 平仓时间[10] - 留空，持仓中无需填写
                                row_values[11] = holding_time_str  # 持仓时间列 (索引11)
                                row_values[12] = "多" if side == 'long' else ("空" if side == 'short' else "待机")  # 方向列 (索引12)
                                row_values[13] = f"{quantity:.4f}" if quantity else ""  # 数量列 (索引13)
                                row_values[14] = f"{leverage:.0f}x" if symbol_type == "合约" else ""  # 倍数列 (索引14)

                                # 增强手续费计算 - 多层级数据源优先级
                                fee_value = 0.0
                                fee_source = "估算"
                                
                                # 第一优先级：trade_status_details中的实际手续费
                                if status_detail.get('actual_fee') is not None:
                                    fee_value = float(status_detail.get('actual_fee'))
                                    fee_source = "API记录"
                                # 第二优先级：实时持仓数据中的手续费
                                elif position_data and position_data.get('fee') is not None:
                                    fee_value = float(position_data.get('fee', 0))
                                    fee_source = "实时API"
                                # 第三优先级：根据交易类型精确估算
                                elif entry_price and quantity:
                                    # 根据交易品种类型使用不同费率
                                    if ":" in symbol:  # 合约交易
                                        fee_rate = 0.0002  # 合约maker费率
                                    else:  # 现货交易
                                        fee_rate = 0.001   # 现货费率
                                    fee_value = entry_price * quantity * fee_rate
                                    fee_source = "估算"
                                
                                # 显示手续费（保留4位小数以提高精度）
                                if fee_value > 0:
                                    row_values[15] = f"{fee_value:.4f}"
                                else:
                                    row_values[15] = "0.0000"
                                
                                # 调试日志记录手续费来源
                                if fee_value > 0:
                                    logging.debug(f"UI_FEE: {symbol} 手续费 {fee_value:.4f} (来源: {fee_source})")

                                row_values[16] = f"{pnl:.2f}"  # 盈亏列 (索引16)
                                row_values[17] = f"{pnl_pct:.2f}%"  # 收益率列 (索引17)
                                row_values[18] = f"{margin:.2f}"  # 占用账户资金列 (索引18)
                                row_values[19] = margin_ratio_str  # 占用账户资金比例列 (索引19)
                                row_values[20] = f"{total_account_balance_usdt:.2f}"  # 账户余额列 (索引20)
                    
                            else: # 无实际持仓，但可能存在于 trade_status_details 中
                                selection_strategy = status_detail.get('selection_strategy', 'N/A')
                                trading_strategy = status_detail.get('trading_strategy', 'N/A')
                                current_status = status_detail.get('status', '未知')

                                # 中文状态映射
                                status_translation_map = {
                                    "open": "已开仓",  # 对应截图中的 'open'
                                    "opening": "开仓中",
                                    "closed": "已平仓",
                                    "closed_by_sl_tp": "止盈止损平仓",
                                    "closed_by_management": "手动平仓",
                                    # "未知": "未知", # 本身是中文，无需映射
                                    # "待机": "待机"  # 本身是中文，且在其他地方已处理
                                }
                                translated_status = status_translation_map.get(current_status, current_status)
                                
                                # 修复列索引映射 - 确保与持仓情况下的映射一致
                                row_values[1] = translated_status  # 状态列 (索引1)
                                row_values[2] = symbol_type  # 类型列 (索引2)
                                row_values[3] = selection_strategy  # 选择策略列 (索引3)
                                row_values[4] = trading_strategy  # 交易策略列 (索引4)
                                row_values[5] = symbol  # 交易品种列 (索引5)
                                row_values[6] = current_price_str  # 最新价格列 (索引6)
                                
                                if current_status == 'opening':
                                     entry_time_s_status = status_detail.get('entry_time',0)
                                     row_values[8] = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(entry_time_s_status)) if entry_time_s_status else ""  # 建仓时间列 (索引8)
                                     row_values[13] = f"{status_detail.get('quantity', ''):.4f}" if status_detail.get('quantity') else ""  # 数量列 (索引13)
                                elif current_status == 'closed' or current_status == 'closed_by_sl_tp' or current_status == 'closed_by_management':
                                    # 对于已平仓的订单，显示完整交易信息
                                    row_values[7] = f"{status_detail.get('entry_price', ''):.4f}" if status_detail.get('entry_price') else ""  # 建仓价格列 (索引7)
                                    entry_time_s_status = status_detail.get('entry_time',0)
                                    row_values[8] = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(entry_time_s_status)) if entry_time_s_status else ""  # 建仓时间列 (索引8)
                                    row_values[9] = f"{status_detail.get('close_price', ''):.4f}" if status_detail.get('close_price') else ""  # 平仓价格列 (索引9)
                                    close_time_s_status = status_detail.get('close_time_ts', 0) # 假设平仓时间戳存储为 close_time_ts
                                    if not close_time_s_status and 'last_update' in status_detail: # 备用
                                        close_time_s_status = status_detail['last_update']
                                    row_values[10] = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(close_time_s_status)) if close_time_s_status else ""  # 平仓时间列 (索引10)
                                    row_values[13] = f"{status_detail.get('quantity', ''):.4f}" if status_detail.get('quantity') else ""  # 数量列 (索引13)
                                    
                                    # 显示平仓交易的盈亏信息
                                    final_pnl = status_detail.get('final_pnl', 0)
                                    final_pnl_pct = status_detail.get('final_pnl_pct', 0)
                                    row_values[16] = f"{final_pnl:.2f}" if final_pnl else "0.00"  # 盈亏列 (索引16)
                                    row_values[17] = f"{final_pnl_pct:.2f}%" if final_pnl_pct else "0.00%"  # 收益率列 (索引17)

                                # 对于非持仓行，手续费等也应显示为空或0
                                row_values[15] = status_detail.get('actual_fee', '0.00') if status_detail.get('actual_fee') else "0.00"  # 手续费列 (索引15)
                        
                        except Exception as e_position_process:
                            logging.error(f"处理品种数据时出错 {symbol if 'symbol' in locals() else 'unknown'}: {e_position_process}")
                            # 设置默认值确保UI不中断
                            row_values[11] = "数据错误"
                            row_values[12] = "N/A"
                        
                    except Exception as e_symbol_process:
                        logging.error(f"处理品种 {symbol if 'symbol' in locals() else 'unknown'} 时出错: {e_symbol_process}")
                        # 使用默认值填充行，确保UI不中断
                        row_values[1] = "错误"
                        row_values[5] = symbol if 'symbol' in locals() else "未知品种"
                        row_values[11] = "处理错误"

                row_values[20] = f"{total_account_balance_usdt:.2f}" # 账户余额始终显示
                all_rows_data.append(row_values)

            # 3. 高效智能表格更新 - 优化增量更新机制
            if not hasattr(self, '_last_table_data'):
                self._last_table_data = []
                self._table_update_count = 0
                self._table_hash_cache = {}
            
            # 使用哈希快速检测数据变化
            data_changed = False
            changed_rows = set()
            
            if len(all_rows_data) != len(self._last_table_data):
                data_changed = True
                logging.debug(f"表格行数变化: {len(self._last_table_data)} -> {len(all_rows_data)}")
            else:
                # 高效的行级变化检测
                for i, new_row in enumerate(all_rows_data):
                    # 计算关键字段的哈希值（状态[1]、价格[6]、盈亏[16]、收益率[17]、数量[7]）
                    key_indices = [1, 6, 7, 16, 17]
                    key_data = tuple(new_row[idx] if idx < len(new_row) else '' for idx in key_indices)
                    row_hash = hash(key_data)
                    
                    old_hash = self._table_hash_cache.get(i, None)
                    if row_hash != old_hash:
                        data_changed = True
                        changed_rows.add(i)
                        self._table_hash_cache[i] = row_hash
            
            # 智能更新策略：数据变化或每20次强制更新
            self._table_update_count += 1
            force_update = self._table_update_count % 20 == 0  # 降低强制更新频率
            
            if data_changed or force_update:
                # 获取当前表格项
                current_items = self.positions_tree.get_children()
                
                # 如果行数不匹配，需要重建表格
                if len(current_items) != len(all_rows_data):
                    # 完全重建
                    for item in current_items:
                        self.positions_tree.delete(item)
                    for row_data in all_rows_data:
                        self.positions_tree.insert("", "end", values=row_data)
                    logging.debug(f"表格完全重建: {len(all_rows_data)}行")
                    # 重建哈希缓存
                    self._table_hash_cache.clear()
                    for i, row_data in enumerate(all_rows_data):
                        key_indices = [1, 6, 7, 16, 17]
                        key_data = tuple(row_data[idx] if idx < len(row_data) else '' for idx in key_indices)
                        self._table_hash_cache[i] = hash(key_data)
                else:
                    # 精确增量更新：只更新变化的行
                    updated_rows = 0
                    if changed_rows:  # 只更新已知变化的行
                        for i in changed_rows:
                            if i < len(current_items) and i < len(all_rows_data):
                                item = current_items[i]
                                self.positions_tree.item(item, values=all_rows_data[i])
                                updated_rows += 1
                    else:  # 强制更新时的全量检查
                        for i, (item, new_row_data) in enumerate(zip(current_items, all_rows_data)):
                            current_values = self.positions_tree.item(item, 'values')
                            if current_values != tuple(new_row_data):
                                self.positions_tree.item(item, values=new_row_data)
                                updated_rows += 1
                    
                    if updated_rows > 0:
                        logging.debug(f"表格精确更新: {updated_rows}/{len(all_rows_data)}行 (变化行: {len(changed_rows)})")
                
                # 更新缓存
                self._last_table_data = [row[:] for row in all_rows_data]  # 深拷贝
                
                if force_update:
                    logging.info(f"表格强制更新(第{self._table_update_count}次) - 性能优化版本")
            else:
                logging.debug("表格数据无变化，跳过更新")
            
            # 性能监控和状态更新
            if not hasattr(self, '_performance_stats'):
                self._performance_stats = {
                    'update_count': 0,
                    'total_update_time': 0,
                    'cache_hits': 0,
                    'api_calls': 0,
                    'last_reset_time': current_time
                }
            
            # 计算本次更新耗时
            update_duration = time.time() - current_time
            self._performance_stats['update_count'] += 1
            self._performance_stats['total_update_time'] += update_duration
            
            # 每100次更新重置统计
            if self._performance_stats['update_count'] % 100 == 0:
                avg_time = self._performance_stats['total_update_time'] / self._performance_stats['update_count']
                logging.info(f"UI性能统计(100次): 平均更新时间{avg_time:.3f}s, 缓存命中率{self._performance_stats.get('cache_hits', 0)}%")
                self._performance_stats = {
                    'update_count': 0,
                    'total_update_time': 0,
                    'cache_hits': 0,
                    'api_calls': 0,
                    'last_reset_time': current_time
                }
            
            # 更新状态栏（包含性能信息）
            if hasattr(self, 'main_status_var') and self.main_status_var:
                status_text = f"自动交易中 (UI已更新 {update_duration:.2f}s)"
                if hasattr(self, '_price_cache') and len(self._price_cache) > 0:
                    status_text += f" | 价格缓存: {len(self._price_cache)}项"
                if hasattr(self, '_positions_cache') and len(self._positions_cache) > 0:
                    status_text += f" | 持仓: {len(self._positions_cache)}个"
                self.main_status_var.set(status_text)

            # 内存管理和缓存清理
            self._cleanup_caches_if_needed(current_time)
            
        except Exception as e_update_table:
            logging.error(f"更新主交易表格失败: {str(e_update_table)}")
            logging.error(traceback.format_exc())
            if hasattr(self, 'main_status_var') and self.main_status_var:
                self.main_status_var.set("UI更新错误")
    
    def _cleanup_caches_if_needed(self, current_time):
        """智能缓存清理机制"""
        if not hasattr(self, '_last_cache_cleanup'):
            self._last_cache_cleanup = current_time
            return
        
        # 每10分钟执行一次缓存清理
        if current_time - self._last_cache_cleanup > 600:
            cleanup_count = 0
            
            # 清理过期的价格缓存（超过5分钟）
            if hasattr(self, '_price_cache_time'):
                expired_keys = []
                for key, cache_time in self._price_cache_time.items():
                    if current_time - cache_time > 300:  # 5分钟
                        expired_keys.append(key)
                
                for key in expired_keys:
                    symbol = key.replace('price_', '')
                    if key in self._price_cache:
                        del self._price_cache[key]
                    del self._price_cache_time[key]
                    if symbol in self._price_fetch_errors:
                        del self._price_fetch_errors[symbol]
                    cleanup_count += 1
            
            # 清理过期的表格数据缓存
            if hasattr(self, '_last_table_data') and len(self._last_table_data) > 50:
                # 如果表格数据过大，保留最近的数据
                self._last_table_data = self._last_table_data[-30:]
                cleanup_count += 1
            
            # 重置错误计数器（避免永久性的高缓存时间）
            if hasattr(self, '_balance_fetch_errors') and self._balance_fetch_errors > 10:
                self._balance_fetch_errors = max(3, self._balance_fetch_errors // 2)
                cleanup_count += 1
            
            if hasattr(self, '_positions_fetch_errors') and self._positions_fetch_errors > 10:
                self._positions_fetch_errors = max(3, self._positions_fetch_errors // 2)
                cleanup_count += 1
            
            if cleanup_count > 0:
                logging.info(f"缓存清理完成: 清理了{cleanup_count}项过期数据")
            
            self._last_cache_cleanup = current_time
    
    def _load_performance_config(self):
        """加载性能配置文件"""
        try:
            config_path = os.path.join(os.path.dirname(__file__), 'performance_config.json')
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    self.perf_config = json.load(f)
                logging.info("性能配置加载成功")
            else:
                # 使用默认配置
                self.perf_config = {
                    "cache_settings": {
                        "price_cache_duration_min": 5,
                        "price_cache_duration_max": 30,
                        "balance_cache_duration_min": 5,
                        "balance_cache_duration_max": 60,
                        "positions_cache_duration_min": 3,
                        "positions_cache_duration_max": 30,
                        "cache_cleanup_interval": 600
                    },
                    "async_settings": {
                        "max_workers": 5,
                        "batch_size": 10,
                        "timeout_seconds": 10,
                        "single_request_timeout": 2
                    },
                    "ui_settings": {
                        "update_interval_ms": 1000,
                        "force_update_every_n_times": 10,
                        "max_table_rows_cache": 50,
                        "performance_stats_reset_count": 100
                    },
                    "error_handling": {
                        "max_error_count_before_reset": 10,
                        "error_count_reset_factor": 0.5,
                        "log_errors_up_to_count": 3
                    },
                    "api_limits": {
                        "batch_fetch_threshold": 3,
                        "batch_fetch_timeout": 8,
                        "inter_batch_delay_ms": 100
                    }
                }
                logging.info("使用默认性能配置")
        except Exception as e:
            logging.error(f"加载性能配置失败: {e}")
            # 使用最基本的默认配置
            self.perf_config = {"cache_settings": {}, "async_settings": {}, "ui_settings": {}, "error_handling": {}, "api_limits": {}}
    
    def _init_real_time_monitor(self):
        """初始化实时监控器"""
        try:
            # 导入实时监控模块
            from real_time_monitor import RealTimeMonitor
            
            # 合并配置
            monitor_config = self.config.copy()
            if hasattr(self, 'perf_config'):
                monitor_config.update(self.perf_config)
            
            self.real_time_monitor = RealTimeMonitor(monitor_config)
            logging.info("实时监控器初始化成功")
        except Exception as e:
            logging.error(f"实时监控器初始化失败: {e}")
            self.real_time_monitor = None
    
    def _get_high_risk_positions(self):
        """获取高风险持仓列表"""
        high_risk_symbols = []
        try:
            if not self.real_time_monitor:
                return high_risk_symbols
            
            # 获取当前持仓
            current_positions = self.exchange.fetch_positions()
            active_positions = [pos for pos in current_positions if float(pos.get('contracts', 0)) > 0]
            
            for position in active_positions:
                symbol = position['symbol']
                # 分析持仓健康状态
                health = self.real_time_monitor._analyze_position_health(position)
                
                # 高风险或严重风险的持仓优先平仓
                if health.risk_level in ['high', 'critical'] or health.recommendation in ['close', 'reduce']:
                    high_risk_symbols.append(symbol)
                    logging.info(f"识别高风险持仓: {symbol}, 风险等级: {health.risk_level}, 建议: {health.recommendation}")
            
            # 按风险等级排序，严重风险优先
            high_risk_symbols.sort(key=lambda s: self._get_risk_priority(s))
            
        except Exception as e:
            logging.error(f"获取高风险持仓失败: {e}")
        
        return high_risk_symbols
    
    def _get_risk_priority(self, symbol):
        """获取风险优先级，数值越小优先级越高"""
        try:
            if not self.real_time_monitor:
                return 999
            
            # 获取持仓信息
            positions = self.exchange.fetch_positions()
            position = next((pos for pos in positions if pos['symbol'] == symbol), None)
            
            if position:
                health = self.real_time_monitor._analyze_position_health(position)
                if health.risk_level == 'critical':
                    return 1
                elif health.risk_level == 'high':
                    return 2
                elif health.risk_level == 'medium':
                    return 3
                else:
                    return 4
        except Exception:
            pass
        
        return 999
    
    def _generate_performance_report(self):
        """生成性能统计报告"""
        try:
            if not hasattr(self, 'real_time_monitor') or not self.real_time_monitor:
                return
            
            # 获取实时监控数据
            position_history = getattr(self.real_time_monitor, 'position_history', {})
            portfolio_risk = getattr(self.real_time_monitor, 'portfolio_risk', 0)
            
            # 统计数据
            total_positions = len(position_history)
            high_risk_count = sum(1 for data in position_history.values() 
                                if isinstance(data, dict) and data.get('risk_level') == 'high')
            medium_risk_count = sum(1 for data in position_history.values() 
                                  if isinstance(data, dict) and data.get('risk_level') == 'medium')
            low_risk_count = sum(1 for data in position_history.values() 
                               if isinstance(data, dict) and data.get('risk_level') == 'low')
            
            # 计算平均盈亏
            pnl_values = []
            for data in position_history.values():
                if isinstance(data, dict):
                    current_pnl = data.get('current_pnl_percent', 0)
                    if current_pnl != 0:
                        pnl_values.append(current_pnl)
            
            avg_pnl = sum(pnl_values) / len(pnl_values) if pnl_values else 0
            
            # 统计优化功能使用情况
            optimization_stats = {
                'smart_close_enabled': hasattr(self, 'real_time_monitor') and self.real_time_monitor is not None,
                'smart_open_enabled': hasattr(self, 'real_time_monitor') and self.real_time_monitor is not None,
                'dynamic_stop_enabled': hasattr(self, 'real_time_monitor') and self.real_time_monitor is not None
            }
            
            # 生成报告
            report = f"""
=== 实时监控性能报告 ===
总持仓数量: {total_positions}
风险分布: 高风险({high_risk_count}) | 中风险({medium_risk_count}) | 低风险({low_risk_count})
投资组合风险: {portfolio_risk:.3f}
平均盈亏: {avg_pnl*100:.2f}%
活跃持仓数: {len(pnl_values)}
智能优化状态:
  - 智能平仓优化: {'✅ 启用' if optimization_stats['smart_close_enabled'] else '❌ 未启用'}
  - 智能开仓优化: {'✅ 启用' if optimization_stats['smart_open_enabled'] else '❌ 未启用'}
  - 动态止损优化: {'✅ 启用' if optimization_stats['dynamic_stop_enabled'] else '❌ 未启用'}
========================
"""
            
            logging.info(report)
            
            # 如果有UI状态变量，也更新状态
            if hasattr(self, 'main_status_var') and self.main_status_var:
                status_text = f"智能监控中 | 持仓:{total_positions} | 高风险:{high_risk_count} | 平均盈亏:{avg_pnl*100:.1f}%"
                self.main_status_var.set(status_text)
                
        except Exception as e:
            logging.error(f"生成性能报告失败: {e}")
     
    def _async_fetch_data(self, symbols_to_fetch):
        """异步批量获取数据"""
        if not symbols_to_fetch:
            return {}
        
        results = {}
        
        # 使用线程池并发获取数据（使用配置参数）
        max_workers = self.perf_config.get('async_settings', {}).get('max_workers', 5)
        timeout_seconds = self.perf_config.get('async_settings', {}).get('timeout_seconds', 10)
        single_timeout = self.perf_config.get('async_settings', {}).get('single_request_timeout', 2)
        
        with ThreadPoolExecutor(max_workers=min(max_workers, len(symbols_to_fetch))) as executor:
            # 提交价格获取任务
            price_futures = {}
            for symbol in symbols_to_fetch:
                if symbol not in self._price_cache or self._is_price_cache_expired(symbol):
                    future = executor.submit(self._fetch_single_price, symbol)
                    price_futures[future] = symbol
            
            # 收集结果（使用配置的超时参数）
            for future in as_completed(price_futures, timeout=timeout_seconds):
                symbol = price_futures[future]
                try:
                    price = future.result(timeout=single_timeout)
                    if price is not None:
                        cache_key = f"price_{symbol}"
                        self._price_cache[cache_key] = price
                        self._price_cache_time[cache_key] = time.time()
                        results[symbol] = price
                        
                        # 重置错误计数
                        if symbol in self._price_fetch_errors:
                            self._price_fetch_errors[symbol] = 0
                            
                except Exception as e:
                    logging.warning(f"异步获取{symbol}价格失败: {e}")
                    # 增加错误计数
                    if symbol not in self._price_fetch_errors:
                        self._price_fetch_errors[symbol] = 0
                    self._price_fetch_errors[symbol] += 1
        
        return results
    
    def _fetch_single_price(self, symbol):
        """获取单个交易对价格"""
        try:
            if hasattr(self, 'exchange') and self.exchange:
                ticker = self.exchange.fetch_ticker(symbol)
                return ticker.get('last') or ticker.get('close')
        except Exception as e:
            logging.debug(f"获取{symbol}价格失败: {e}")
            return None
        return None

    def pause_trading(self):
        if not getattr(self, '_is_trading', False) and not getattr(self, '_is_paused', False):
            try: messagebox.showinfo("提示", "交易未启动！")
            except Exception as e: logging.error(f"显示信息对话框失败: {str(e)}")
            return
        
        if getattr(self, '_is_paused', False): # 如果已经是暂停状态
            # 切换为继续交易
            logging.info("从暂停状态切换为继续交易...")
            self._is_paused = False
            if hasattr(self, 'main_status_var'): self.main_status_var.set("自动交易中...")
            if hasattr(self, 'btn_pause'): self.btn_pause.config(text="暂停交易") # 恢复按钮文字
            messagebox.showinfo("交易继续", "自动交易已恢复。")
            return

        # 执行暂停交易操作 (首次点击暂停时)
        logging.info("执行暂停交易操作...")
        self._is_paused = True # 设置暂停标志
        if hasattr(self, 'main_status_var'): self.main_status_var.set("正在暂停...")
        if hasattr(self, 'btn_pause'): self.btn_pause.config(text="继续交易") # 更改按钮文字

        # 可选：暂停时是否平仓，这里改为不主动平仓，仅暂停新开仓和管理
        # 如果需要暂停时平仓，可以取消下面平仓逻辑的注释
        messagebox.showinfo("交易暂停", "自动交易已暂停。\n现有持仓将按止盈止损策略管理，不会新开仓。")
        logging.info("交易已暂停，不会新开仓。现有持仓将由止盈止损策略管理。")
        if hasattr(self, 'main_status_var'): self.main_status_var.set("已暂停")


    def stop_trading(self):
        logging.info("执行停止交易操作...")
        self._is_trading = False
        self._is_paused = False  # 清除暂停状态
        if hasattr(self, 'main_status_var') and self.main_status_var:
            self.main_status_var.set("停止指令已发送，后台处理中...")

        # 启动后台线程执行停止和平仓操作，主函数快速返回
        logging.info("启动后台线程执行停止交易后的清理操作。")
        stop_thread = threading.Thread(target=self._stop_trading_task, daemon=True, name="StopTrading")
        self._register_thread(stop_thread)
        stop_thread.start()
        
        if hasattr(self, 'btn_pause') and self.btn_pause: # 恢复暂停按钮状态
            self.btn_pause.config(text="暂停交易")

    def _stop_trading_task(self):
        """强化版后台任务：无条件停止所有交易并强制平仓"""
        logging.info("强化版_stop_trading_task 开始执行...")
        
        try:
            # 第一步：停止实时监控器
            if self.real_time_monitor:
                self.real_time_monitor.stop_monitoring()
                logging.info("实时监控器已停止")
            
            # 第二步：强制停止所有交易线程和定时任务
            self._force_stop_all_threads()
            
            # 第二步：取消所有未完成的订单
            self._cancel_all_open_orders()
            
            # 第三步：强制平仓所有持仓
            closed_count, failed_symbols = self._force_close_all_positions()
            
            # 第四步：验证和重试机制
            retry_count = 0
            max_retries = 3
            while retry_count < max_retries and failed_symbols:
                retry_count += 1
                logging.info(f"第 {retry_count} 次重试平仓失败的品种: {failed_symbols}")
                if hasattr(self, 'main_status_var') and self.main_status_var:
                    self._queue_ui_update(lambda r=retry_count: self.main_status_var.set(f"重试平仓中 ({r}/{max_retries})"))
                
                time.sleep(2)  # 等待2秒后重试
                retry_closed, retry_failed = self._retry_close_positions(failed_symbols)
                closed_count += retry_closed
                failed_symbols = retry_failed
            
            # 更新最终状态
            final_status_msg = "已强制停止"
            result_message = f"交易已强制停止。\n成功平仓: {closed_count} 个品种。"
            if failed_symbols:
                result_message += f"\n仍有 {len(failed_symbols)} 个品种平仓失败: {', '.join(failed_symbols)}"
                result_message += "\n建议手动检查并处理剩余持仓。"
                final_status_msg = "已强制停止 (部分平仓失败)"
            else:
                result_message += "\n所有持仓已成功平仓。"
            
            if hasattr(self, 'main_status_var') and self.main_status_var:
                 self._queue_ui_update(lambda: self.main_status_var.set(final_status_msg))
            # 防止重复弹窗
            if hasattr(self, 'main_root') and self.main_root.winfo_exists() and not self._dialog_states.get('stop_trading_shown', False):
                 self._dialog_states['stop_trading_shown'] = True
                 self.main_root.after(0, lambda: self._show_stop_trading_result(result_message))
            logging.info(result_message)

        except Exception as e_task:
            logging.error(f"强化版_stop_trading_task 发生严重错误: {str(e_task)}\n{traceback.format_exc()}")
            if hasattr(self, 'main_status_var') and self.main_status_var:
                self._queue_ui_update(lambda: self.main_status_var.set("已停止 (发生错误)"))
            # 防止重复弹出错误对话框
            if hasattr(self, 'main_root') and self.main_root.winfo_exists() and not self._dialog_states.get('error_dialog_shown', False):
                self._dialog_states['error_dialog_shown'] = True
                self.main_root.after(0, lambda: self._show_error_dialog(f"强制停止过程中发生错误: {str(e_task)}\n请手动检查交易状态。"))
        finally:
            logging.info("强化版停止交易任务执行完毕。")
            # 确保最终状态显示已停止
            if hasattr(self, 'main_status_var') and self.main_status_var and "停止" not in self.main_status_var.get():
                self._queue_ui_update(lambda: self.main_status_var.set("已强制停止"))
    
    def _show_stop_trading_result(self, message):
        """显示停止交易结果，防止重复弹窗"""
        try:
            messagebox.showinfo("强制停止交易完成", message)
        finally:
            # 重置弹窗状态，允许下次显示
            self._dialog_states['stop_trading_shown'] = False
    
    def _show_error_dialog(self, message):
         """显示错误对话框，防止重复弹窗"""
         try:
             messagebox.showerror("停止交易错误", message)
         finally:
             # 重置弹窗状态，允许下次显示
             self._dialog_states['error_dialog_shown'] = False
     
    def _show_warning_dialog(self, title, message):
        """显示警告对话框，防止重复弹窗"""
        if not self._dialog_states.get('warning_dialog_shown', False):
            self._dialog_states['warning_dialog_shown'] = True
            try:
                messagebox.showwarning(title, message)
            finally:
                # 重置弹窗状态，允许下次显示
                self._dialog_states['warning_dialog_shown'] = False
    
    def _force_stop_all_threads(self):
        """强制停止所有交易相关线程和定时任务"""
        try:
            logging.info("强制停止所有线程和定时任务...")
            
            # 清理已完成的线程
            self._cleanup_finished_threads()
            
            # 停止所有活跃线程
            with self._thread_lock:
                active_threads = list(self._active_threads)
            
            for thread in active_threads:
                if thread.is_alive():
                    logging.info(f"等待线程 {thread.name} 结束...")
                    thread.join(timeout=1)  # 等待1秒
                    if thread.is_alive():
                        logging.warning(f"线程 {thread.name} 未能及时结束")
                self._unregister_thread(thread)
            
            # 停止主交易线程
            if hasattr(self, 'trade_thread') and self.trade_thread and self.trade_thread.is_alive():
                logging.info("等待主交易线程结束...")
                self.trade_thread.join(timeout=2)  # 等待2秒
                if self.trade_thread.is_alive():
                    logging.warning("主交易线程未能及时结束，将强制继续")
            
            # 取消所有定时任务
            if hasattr(self, 'main_root') and self.main_root:
                try:
                    # 取消所有after调度的任务
                    self.main_root.after_cancel('all')
                    logging.info("已取消所有定时任务")
                except:
                    pass
            
            # 设置停止标志
            self._is_trading = False
            self._is_paused = False
            
            logging.info("线程和定时任务停止完成")
            
        except Exception as e:
            logging.error(f"停止线程时发生错误: {str(e)}")
    
    def _cancel_all_open_orders(self):
        """取消所有未完成的订单"""
        try:
            logging.info("开始取消所有未完成订单...")
            if hasattr(self, 'main_status_var') and self.main_status_var:
                self._queue_ui_update(lambda: self.main_status_var.set("取消订单中..."))
            
            if not self.exchange:
                logging.warning("交易所对象未初始化，跳过取消订单")
                return
            
            # 获取所有未完成订单
            open_orders = self.exchange.fetch_open_orders()
            cancelled_count = 0
            
            for order in open_orders:
                try:
                    symbol = order['symbol']
                    order_id = order['id']
                    self.exchange.cancel_order(order_id, symbol)
                    cancelled_count += 1
                    logging.info(f"已取消订单: {symbol} - {order_id}")
                    time.sleep(0.1)  # 短暂延迟避免API限制
                except Exception as e_order:
                    logging.warning(f"取消订单失败 {order.get('symbol', 'Unknown')}: {str(e_order)}")
            
            logging.info(f"订单取消完成，共取消 {cancelled_count} 个订单")
            
        except Exception as e:
            logging.error(f"取消订单时发生错误: {str(e)}")
    
    def _force_close_all_positions(self):
        """强制平仓所有持仓"""
        try:
            logging.info("开始强制平仓所有持仓...")
            if hasattr(self, 'main_status_var') and self.main_status_var:
                self._queue_ui_update(lambda: self.main_status_var.set("强制平仓中..."))
            
            if not self.exchange:
                logging.warning("交易所对象未初始化，跳过平仓")
                return 0, []
            
            # 获取当前持仓
            current_positions = self.fetch_positions(self.exchange)
            logging.info(f"获取到 {len(current_positions)} 个持仓")
            
            if not current_positions:
                logging.info("当前无持仓，跳过平仓")
                return 0, []
            
            closed_count = 0
            failed_symbols = []
            
            # 遍历所有持仓并强制平仓
            for position in current_positions:
                try:
                    symbol = position.get('symbol')
                    side = position.get('side')
                    qty_field = 'contracts' if position.get('type') == 'contract' else 'amount'
                    amount = position.get(qty_field, 0)
                    
                    if float(amount) > 0:  # 只处理有持仓的
                        logging.info(f"强制平仓: {symbol}, 方向: {side}, 数量: {amount}")
                        if hasattr(self, 'main_status_var') and self.main_status_var:
                            self._queue_ui_update(lambda s=symbol: self.main_status_var.set(f"强制平仓: {s}"))
                        
                        # 调用强化版平仓函数
                        result = self._enhanced_close_position(symbol, side, amount)
                        if result:
                            closed_count += 1
                            logging.info(f"强制平仓成功: {symbol}")
                        else:
                            failed_symbols.append(symbol)
                            logging.warning(f"强制平仓失败: {symbol}")
                        
                        time.sleep(0.2)  # 短暂延迟避免API限制
                            
                except Exception as e_pos:
                    symbol_name = position.get('symbol', 'Unknown')
                    failed_symbols.append(symbol_name)
                    logging.error(f"强制平仓 {symbol_name} 时发生错误: {str(e_pos)}")
            
            return closed_count, failed_symbols
            
        except Exception as e:
            logging.error(f"强制平仓过程发生错误: {str(e)}")
            return 0, []
    
    def _enhanced_close_position(self, symbol, side, amount):
        """增强版平仓函数，支持多种平仓策略"""
        try:
            # 策略1：使用原有的close_position函数
            try:
                result = self.close_position(self.exchange, symbol, side)
                if result:
                    return True
            except Exception as e1:
                logging.warning(f"策略1平仓失败 {symbol}: {str(e1)}")
            
            # 策略2：直接市价单平仓
            try:
                close_side = 'sell' if side == 'long' else 'buy'
                is_contract = ":" in symbol
                
                params = {}
                if is_contract:
                    params.update({
                        'tdMode': 'cross',
                        'posSide': side
                    })
                
                order = self.exchange.create_order(symbol, 'market', close_side, amount, None, params)
                if order:
                    logging.info(f"策略2平仓成功 {symbol}: {order.get('id', 'Unknown')}")
                    return True
            except Exception as e2:
                logging.warning(f"策略2平仓失败 {symbol}: {str(e2)}")
            
            # 策略3：分批平仓
            try:
                batch_size = float(amount) / 2  # 分2批
                success_count = 0
                for i in range(2):
                    try:
                        close_side = 'sell' if side == 'long' else 'buy'
                        batch_amount = batch_size if i == 0 else float(amount) - batch_size
                        
                        params = {}
                        if ":" in symbol:
                            params.update({
                                'tdMode': 'cross',
                                'posSide': side
                            })
                        
                        order = self.exchange.create_order(symbol, 'market', close_side, batch_amount, None, params)
                        if order:
                            success_count += 1
                            logging.info(f"策略3分批平仓 {symbol} 第{i+1}批成功")
                            time.sleep(0.3)  # 短暂等待
                    except Exception as e_batch:
                        logging.warning(f"策略3分批平仓 {symbol} 第{i+1}批失败: {str(e_batch)}")
                
                return success_count > 0  # 只要有一批成功就认为部分成功
            except Exception as e3:
                logging.warning(f"策略3分批平仓失败 {symbol}: {str(e3)}")
            
            return False
            
        except Exception as e:
            logging.error(f"增强版平仓函数发生错误 {symbol}: {str(e)}")
            return False
    
    def _retry_close_positions(self, failed_symbols):
        """重试平仓失败的品种"""
        closed_count = 0
        still_failed = []
        
        try:
            if not self.exchange:
                return 0, failed_symbols
            
            # 重新获取持仓信息
            current_positions = self.fetch_positions(self.exchange)
            
            for symbol in failed_symbols:
                try:
                    # 查找该品种的持仓
                    position_found = False
                    for position in current_positions:
                        if position.get('symbol') == symbol:
                            side = position.get('side')
                            qty_field = 'contracts' if position.get('type') == 'contract' else 'amount'
                            amount = position.get(qty_field, 0)
                            
                            if float(amount) > 0:
                                position_found = True
                                logging.info(f"重试平仓: {symbol}, 方向: {side}, 数量: {amount}")
                                
                                result = self._enhanced_close_position(symbol, side, amount)
                                if result:
                                    closed_count += 1
                                    logging.info(f"重试平仓成功: {symbol}")
                                else:
                                    still_failed.append(symbol)
                                    logging.warning(f"重试平仓仍然失败: {symbol}")
                                break
                    
                    if not position_found:
                        logging.info(f"重试时未找到持仓，可能已平仓: {symbol}")
                        
                except Exception as e_retry:
                    still_failed.append(symbol)
                    logging.error(f"重试平仓 {symbol} 时发生错误: {str(e_retry)}")
            
            return closed_count, still_failed
            
        except Exception as e:
            logging.error(f"重试平仓过程发生错误: {str(e)}")
            return 0, failed_symbols
            # 确保即使发生错误，最终状态也是某种形式的“已停止”
            if hasattr(self, 'main_status_var') and self.main_status_var and "停止" not in self.main_status_var.get():
                self._queue_ui_update(lambda: self.main_status_var.set("已停止 (任务结束)"))

    def load_encryption_key(self):
        """加载或生成加密密钥，使用Fernet最佳实践"""
        key_file = "encryption_key.key"
        try:
            if os.path.exists(key_file):
                with open(key_file, "rb") as f:
                    key = f.read()
                # 验证密钥格式
                Fernet(key)  # 这会抛出异常如果密钥无效
                return key
            else:
                # 生成新密钥
                key = Fernet.generate_key()
                with open(key_file, "wb") as f:
                    f.write(key)
                logging.info("已生成新的加密密钥")
                return key
        except Exception as e:
            logging.error(f"加密密钥处理失败: {str(e)}")
            # 生成备用密钥
            key = Fernet.generate_key()
            try:
                with open(key_file, "wb") as f:
                    f.write(key)
            except:
                pass
            return key

    def encrypt_data(self, data):
        """使用Fernet安全加密数据"""
        if not data or not isinstance(data, str):
            return ""
        try:
            f = Fernet(self.encryption_key)
            return f.encrypt(data.encode('utf-8')).decode('utf-8')
        except Exception as e:
            logging.error(f"数据加密失败: {str(e)}")
            return ""

    def decrypt_data(self, encrypted_data):
        """使用Fernet安全解密数据"""
        if not encrypted_data or not isinstance(encrypted_data, str):
            return ""
        try:
            f = Fernet(self.encryption_key)
            return f.decrypt(encrypted_data.encode('utf-8')).decode('utf-8')
        except Exception as e:
            logging.error(f"数据解密失败: {str(e)}")
            return ""

    def load_config(self, force_reload=False):
        current_time = time.time()
        
        # 使用缓存（如果未过期且不强制重载）
        if (not force_reload and self._config_cache is not None and 
            current_time - self._config_cache_time < self._config_cache_timeout):
            return self._config_cache
        
        if not os.path.exists("config.json"):
            default_config = {"simulated": {}, "live": {}}
            with open("config.json", "w", encoding="utf-8") as f:
                json.dump(default_config, f)
            self._config_cache = default_config
            self._config_cache_time = current_time
            return default_config
        try:
            with open("config.json", "r", encoding="utf-8") as f:
                config = json.load(f)
            decrypted_config = {"simulated": {}, "live": {}}
            for mode in ["simulated", "live"]:
                for key, value in config.get(mode, {}).items():
                    decrypted_config[mode][key] = self.decrypt_data(value) if value else ""
            
            # 更新缓存
            self._config_cache = decrypted_config
            self._config_cache_time = current_time
            return decrypted_config
        except Exception as e:
            self.smart_error_handler("配置错误", f"配置文件损坏或密钥错误，请重新配置。\n错误详情：{str(e)}")
            default_config = {"simulated": {}, "live": {}}
            self._config_cache = default_config
            self._config_cache_time = current_time
            return default_config

    def save_config(self, mode, data):
        encrypted_data = {}
        for key, value in data.items():
            encrypted_data[key] = self.encrypt_data(value) if value else ""
        self.config[mode].update(encrypted_data)
        with open("config.json", "w", encoding="utf-8") as f:
            json.dump(self.config, f)
        
        # 清除缓存，确保下次读取最新数据
        self._config_cache = None
        self._config_cache_time = 0

    def check_environment(self):
        required_modules = ["ccxt", "numpy", "requests", "pandas", "cryptography", "matplotlib"]
        missing_modules = []
        for module in required_modules:
            try:
                __import__(module)
            except ImportError:
                missing_modules.append(module)
        ccxt_version = pkg_resources.get_distribution("ccxt").version
        if ccxt_version < RECOMMENDED_CCXT_VERSION:
            messagebox.showwarning("CCXT版本警告", f"CCXT库版本较低，建议升级到 {RECOMMENDED_CCXT_VERSION} 及以上。当前版本：{ccxt_version}")
        if missing_modules:
            messagebox.showerror("缺少依赖", f"请安装以下依赖：{', '.join(missing_modules)}")
            sys.exit(1)

    def check_network(self, proxies=None):
        test_url = "https://www.okx.com"
        try:
            resp = requests.get(test_url, proxies=proxies, timeout=5)
            if resp.status_code == 200:
                return True
            else:
                logging.error(f"网络检测失败，状态码：{resp.status_code}")
                return False
        except Exception as e:
            logging.error(f"网络检测异常：{str(e)}")
            return False

    def test_api_keys(self, apiKey, secret, password, proxies=None):
        try:
            exchange = ccxt.okx({
                'apiKey': apiKey,
                'secret': secret,
                'password': password,
                'enableRateLimit': True,
                'proxies': proxies or None
            })
            exchange.load_markets()
            # ======= 核心代理诊断与友好提示 =======
            # 先用requests检测代理是否可用
            import requests
            test_proxies = proxies or {
                "http": "http://127.0.0.1:10808",
                "https": "http://127.0.0.1:10808"
            }
            try:
                resp = requests.get("https://www.okx.com/api/v5/public/time", proxies=test_proxies, timeout=10)
                if resp.status_code != 200:
                    raise Exception(f"代理可达但OKX接口返回{resp.status_code}")
            except Exception as e:
                msg = f"检测到你的代理设置无效，Python程序无法通过代理连通欧易API。\n请确认v2rayN已开启全局模式，且本地端口为10808，协议为HTTP。\n错误详情：{str(e)}"
                logging.error(msg)
                raise Exception(msg)
            # ======= 继续API密钥检测 =======
            balance = exchange.fetch_balance()
            return True, balance
        except ccxt.AuthenticationError as e:
            logging.error(f"API密钥认证失败：{str(e)}")
            return False, f"API密钥认证失败：{str(e)}"
        except Exception as e:
            logging.error(f"API验证异常：{str(e)}")
            return False, f"API验证异常：{str(e)}"

    def diagnose(self):
        # 一键诊断功能
        report = []
        if not self.check_network(getattr(self, 'proxies', None)):
            report.append("网络连接欧易失败，请检查网络或代理设置。")
        try:
            ccxt_version = pkg_resources.get_distribution("ccxt").version
            if ccxt_version < RECOMMENDED_CCXT_VERSION:
                report.append(f"CCXT库版本过低，建议升级到 {RECOMMENDED_CCXT_VERSION} 及以上。")
        except Exception as e:
            report.append(f"无法检测CCXT版本：{str(e)}")
        for mode in ["simulated", "live"]:
            keys = self.config.get(mode, {})
            if all(keys.get(k) for k in ["apiKey", "secret", "password"]):
                ok, msg = self.test_api_keys(keys["apiKey"], keys["secret"], keys["password"], getattr(self, 'proxies', None))
                if not ok:
                    report.append(f"{mode}模式API密钥异常：{msg}")
            else:
                report.append(f"{mode}模式API密钥未完整配置。")
        if not report:
            report.append("所有检测通过，无异常。")
        messagebox.showinfo("一键诊断报告", "\n".join(report))
        logging.info("一键诊断报告：" + " | ".join(report))

    def create_order(self, exchange, symbol, order_type, side, amount, price=None, params={}):
        """
        创建订单
        :param exchange: ccxt交易所对象
        :param symbol: 交易对，如 "BTC/USDT:USDT"
        :param order_type: 订单类型，如 "market", "limit"
        :param side: 交易方向，"buy" 或 "sell"
        :param amount: 交易数量
        :param price: 价格，市价单可为None
        :param params: 额外参数
        :return: 订单信息
        """
        final_params = params.copy() # 操作副本，不修改外部传入的params

        try:
            # 记录订单创建信息
            logging.info(f"创建订单: {symbol} {order_type} {side} {amount} {price}")

            # 确保是模拟交易模式
            is_sandbox = exchange.options.get('test', False)
            if is_sandbox:
                # 确保设置了模拟交易标志
                exchange.set_sandbox_mode(True)
                logging.info("确认模拟交易模式已启用")

            # 客户订单ID (确保唯一性和长度)
            # OKEx要求clOrdId长度不超过32个字符，只能包含字母和数字
            timestamp_ms = int(time.time() * 1000)
            # 移除特殊字符，确保字母数字
            symbol_cleaned = "".join(filter(str.isalnum, symbol.split('/')[0]))
            raw_clordid = f"T{symbol_cleaned}{timestamp_ms}{random.randint(100,999)}"
            final_params['clOrdId'] = raw_clordid[:32]
            logging.info(f"生成客户端订单ID: {final_params['clOrdId']} for {symbol}")

            is_contract_order = ":" in symbol or "-SWAP" in symbol.upper()

            # 保证金模式 tdMode (仅合约)
            if is_contract_order:
                if 'tdMode' not in final_params: # 如果外部未提供
                    final_params['tdMode'] = 'cross' # 默认全仓
                logging.info(f"合约 {symbol} 使用 tdMode: {final_params['tdMode']}")
            elif 'tdMode' in final_params: # 现货不应有tdMode
                del final_params['tdMode']

            # 持仓方向 posSide (仅合约, 且账户为双向持仓模式时)
            if is_contract_order:
                # 如果外部调用已在params中提供了posSide，则使用它
                # 否则，根据买卖方向(side)决定开多/开空
                if 'posSide' not in final_params:
                    if side == 'buy':
                        final_params['posSide'] = 'long'
                    else: # side == 'sell'
                        final_params['posSide'] = 'short'
                logging.info(f"合约 {symbol} 使用 posSide: {final_params['posSide']}")
            elif 'posSide' in final_params: # 现货不应有posSide
                del final_params['posSide']

            # 杠杆 lever (仅合约)
            # 杠杆通常通过 exchange.set_leverage() 提前设置。
            # OKEx市价单允许在下单时附带杠杆参数 'lever' (字符串类型)。
            if is_contract_order:
                if 'lever' in final_params: # 如果外部传入了杠杆
                    final_params['lever'] = str(final_params['lever']) # 确保是字符串
                    logging.info(f"合约 {symbol} 订单参数中包含杠杆: {final_params['lever']}")
                # 注意：如果已通过 set_leverage 设置，此处再传 lever 可能会冲突或被忽略，取决于交易所。
                # 对于测试交易杠杆为1，已在 on_smart_select 中尝试 set_leverage。
            elif 'lever' in final_params: # 现货不应有lever
                del final_params['lever']

            # 数量和价格精度处理
            try:
                amount_prec = float(exchange.amount_to_precision(symbol, amount))
                price_prec = None
                if price is not None:
                    price_prec = float(exchange.price_to_precision(symbol, price))
                logging.info(f"订单 {symbol}: 数量 {amount_prec} (原始 {amount}), 价格 {price_prec} (原始 {price}) - 精度处理后")
                amount_to_use = amount_prec
                price_to_use = price_prec
            except Exception as e_prec:
                logging.warning(f"处理数量/价格精度失败 for {symbol}: {str(e_prec)}. 将使用原始值。")
                amount_to_use = amount # 使用原始值继续尝试
                price_to_use = price

            logging.info(f"最终下单参数 for {symbol}: type={order_type}, side={side}, amount={amount_to_use}, price={price_to_use}, params={final_params}")

            created_order = None
            try:
                # CCXT的 create_order 会自动处理市价单不需要price的情况
                created_order = exchange.create_order(symbol, order_type, side, amount_to_use, price_to_use, final_params)
                logging.info(f"订单创建成功: ID={created_order.get('id', '无ID') if created_order else '返回None'}, 详情: {created_order}")
            except ccxt.InsufficientFunds as e_funds:
                logging.error(f"资金不足 {symbol}: {str(e_funds)}")
                raise e_funds # 重新抛出，让上层处理UI提示
            except ccxt.ExchangeError as e_exchange_err:
                logging.error(f"交易所错误 {symbol}: {str(e_exchange_err)}")
                err_msg_lower = str(e_exchange_err).lower()
                # 特别处理与账户模式相关的错误
                if "parameter posside error" in err_msg_lower or \
                   "account mode" in err_msg_lower or \
                   "30012" in str(e_exchange_err) or \
                   "30019" in str(e_exchange_err) or \
                   "position side is required" in err_msg_lower: # 30012: Parameter posSide error, 30019: Account request restricted
                    hint = ("这通常意味着您的OKEx模拟账户仍处于【单向持仓模式】或API权限不足。\n"
                            "请前往OKEx官网，在模拟盘的\"交易设置\"中，将\"持仓方式\"更改为【双向持仓模式】。\n"
                            "同时检查API Key是否已授予\"交易\"权限（模拟盘API Key通常有完整权限）。\n"
                            f"原始错误: {str(e_exchange_err)}")
                    logging.error(hint)
                    # 不在此处弹窗，由调用者处理
                    # messagebox.showerror("下单失败 - 账户模式/权限问题", hint)
                raise e_exchange_err # 重新抛出
            except Exception as e_create_generic:
                logging.error(f"创建订单时发生未知错误 {symbol}: {str(e_create_generic)}")
                logging.error(traceback.format_exc())
                raise e_create_generic

            if created_order is None: # 确保订单对象存在
                raise Exception(f"exchange.create_order 返回 None, 订单创建失败 for {symbol}")

            # 验证订单状态 (可选，但有助于调试)
            try:
                time.sleep(1.5) # 给交易所一点处理时间
                fetched_order_status = exchange.fetch_order(created_order['id'], symbol)
                logging.info(f"获取订单状态 {created_order['id']}: {fetched_order_status.get('status', '未知状态')}")
            except Exception as e_fetch_order_status:
                logging.warning(f"获取订单状态失败 for {created_order['id']}: {str(e_fetch_order_status)}")

            return created_order

        except Exception as e_create_order_top:
            logging.error(f"create_order 函数顶层异常 for {symbol}: {str(e_create_order_top)}")
            logging.error(traceback.format_exc())
            raise e_create_order_top

    def close_position(self, exchange, symbol, position_side):
        """
        平仓，支持现货和合约

        参数:
        - exchange: 交易所对象
        - symbol: 交易对，如 "BTC/USDT:USDT"（合约）或 "BTC/USDT"（现货）
        - position_side: 持仓方向，"long" 或 "short"

        返回:
        - 订单信息
        """
        try:
            # 确保是模拟交易模式
            is_sandbox = exchange.options.get('test', False)
            if is_sandbox:
                # 确保设置了模拟交易标志
                exchange.set_sandbox_mode(True)
                logging.info("确认模拟交易模式已启用")

            # 判断是现货还是合约
            is_spot = ":" not in symbol

            # 获取持仓信息
            logging.info(f"获取持仓信息: {symbol}, 方向: {position_side}")
            positions = exchange.fetch_positions([symbol])
            position = None

            # 查找指定方向的持仓
            for pos in positions:
                if pos['symbol'] == symbol and pos['side'] == position_side:
                    if is_spot:
                        # 现货判断amount
                        if float(pos.get('amount', 0)) > 0:
                            position = pos
                            break
                    else:
                        # 合约判断contracts
                        if float(pos.get('contracts', 0)) > 0:
                            position = pos
                            break

            if not position:
                logging.warning(f"未找到持仓: {symbol}, 方向: {position_side}")
                return None

            # 获取持仓数量（现货和合约的字段名不同）
            if is_spot:
                amount = float(position.get('amount', 0))
            else:
                amount = float(position.get('contracts', 0))

            # 确定平仓方向（与持仓方向相反）
            close_side = 'sell' if position_side == 'long' else 'buy'

            # 准备平仓参数
            params = {}

            if not is_spot:
                # 合约特有参数
                params.update({
                    'tdMode': 'cross'  # 默认使用全仓模式
                })

                # 单独设置posSide参数，确保格式正确
                params['posSide'] = position_side

                # 记录详细的持仓方向信息
                logging.info(f"合约平仓设置持仓方向: {position_side}, 平仓方向: {close_side}")

            # 添加平仓标识
            # OKEx要求clientOrderId长度不超过32个字符，且只能包含字母、数字和下划线
            timestamp = int(time.time())
            random_suffix = random.randint(1000, 9999)
            client_order_id = f"OKXC{timestamp}{random_suffix}"

            # 确保clientOrderId不超过32个字符
            if len(client_order_id) > 32:
                client_order_id = client_order_id[:32]

            logging.info(f"生成平仓客户端订单ID: {client_order_id}")

            # 设置客户端订单ID
            params['clOrdId'] = client_order_id  # OKEx使用clOrdId而不是clientOrderId

            # 记录详细的平仓信息
            logging.info(f"执行平仓: {symbol}, 类型: {'现货' if is_spot else '合约'}, 方向: {close_side}, 数量: {amount}, 参数: {params}")

            # 执行市价平仓
            order = self.create_order(exchange, symbol, 'market', close_side, amount, None, params)

            # 验证平仓是否成功
            try:
                # 等待一小段时间，确保订单已被处理
                time.sleep(1)
                # 再次获取持仓信息，检查是否已平仓
                new_positions = exchange.fetch_positions([symbol])
                position_exists = False

                for pos in new_positions:
                    if pos['symbol'] == symbol and pos['side'] == position_side:
                        if is_spot:
                            # 现货判断amount
                            if float(pos.get('amount', 0)) > 0:
                                position_exists = True
                                break
                        else:
                            # 合约判断contracts
                            if float(pos.get('contracts', 0)) > 0:
                                position_exists = True
                                break

                if position_exists:
                    logging.warning(f"平仓可能未完全成功，仍有持仓: {symbol}, 方向: {position_side}")
                else:
                    logging.info(f"平仓成功确认: {symbol}, 方向: {position_side}")
            except Exception as e:
                logging.warning(f"验证平仓状态失败: {str(e)}")

            return order
        except Exception as e:
            logging.error(f"平仓失败: {str(e)}")
            raise e

    def fetch_positions(self, exchange, symbols=None):
        """
        获取当前持仓，支持现货和合约，带智能缓存优化

        参数:
        - exchange: 交易所对象
        - symbols: 交易对列表，如 ["BTC/USDT:USDT"]，可为None查询所有

        返回:
        - 持仓列表
        """
        try:
            # 初始化缓存变量（如果不存在）
            if not hasattr(self, '_positions_cache'):
                self._positions_cache = {}
                self._positions_cache_time = 0
                self._positions_fetch_errors = 0
            
            # 缓存有效期：3秒（减少API调用频率）
            cache_duration = 3.0
            current_time = time.time()
            
            # 检查缓存是否有效
            if (current_time - self._positions_cache_time < cache_duration and 
                self._positions_cache and 
                self._positions_fetch_errors < 3):
                logging.debug(f"使用持仓缓存数据，缓存时间: {current_time - self._positions_cache_time:.1f}秒")
                return self._positions_cache.get('positions', [])
            
            # 确保是模拟交易模式
            is_sandbox = exchange.options.get('test', False)
            if is_sandbox:
                # 确保设置了模拟交易标志
                exchange.set_sandbox_mode(True)
                if current_time - self._positions_cache_time > 10:  # 每10秒记录一次
                    logging.info("确认模拟交易模式已启用")

            # 获取合约持仓信息
            contract_positions = exchange.fetch_positions(symbols)

            # 过滤出活跃合约持仓
            active_contract_positions = []
            for p in contract_positions:
                if float(p.get('contracts', 0)) > 0:
                    # 标记为合约
                    p['type'] = 'contract'
                    active_contract_positions.append(p)

            # 获取现货持仓信息
            spot_positions = []
            try:
                # 获取现货余额
                balance = exchange.fetch_balance()

                # 过滤出非零余额的现货
                for currency, data in balance.get('total', {}).items():
                    if currency != 'USDT' and float(data) > 0:
                        # 构造现货持仓信息
                        symbol = f"{currency}/USDT"

                        # 获取当前价格
                        try:
                            ticker = exchange.fetch_ticker(symbol)
                            current_price = float(ticker['last'])
                        except Exception:
                            current_price = 0.0

                        # 创建持仓对象
                        spot_position = {
                            'symbol': symbol,
                            'side': 'long',  # 现货只有多头
                            'amount': float(data),
                            'contracts': 0,  # 现货没有合约数量
                            'entryPrice': current_price,  # 暂时使用当前价格，后续可优化
                            'timestamp': int(time.time() * 1000),
                            'type': 'spot'  # 标记为现货
                        }
                        spot_positions.append(spot_position)
            except Exception as e:
                logging.error(f"获取现货持仓失败: {str(e)}")

            # 合并合约和现货持仓
            active_positions = active_contract_positions + spot_positions

            # 更新缓存
            self._positions_cache = {
                'positions': active_positions,
                'contract_count': len(active_contract_positions),
                'spot_count': len(spot_positions)
            }
            self._positions_cache_time = current_time
            self._positions_fetch_errors = 0  # 重置错误计数

            # 记录详细的持仓信息（减少日志频率）
            if active_positions and (current_time - getattr(self, '_last_positions_log_time', 0) > 30):
                self._last_positions_log_time = current_time
                for pos in active_positions:
                    symbol = pos['symbol']
                    side = pos['side']
                    pos_type = pos.get('type', 'contract')

                    if pos_type == 'spot':
                        amount = float(pos.get('amount', 0))
                    else:
                        amount = float(pos.get('contracts', 0))

                    entry_price = float(pos.get('entryPrice', 0))
                    logging.info(f"活跃持仓: {symbol}, 类型: {pos_type}, 方向: {side}, 数量: {amount}, 入场价: {entry_price}")

            # 每30秒记录一次汇总信息
            if current_time - getattr(self, '_last_positions_summary_time', 0) > 30:
                self._last_positions_summary_time = current_time
                logging.info(f"获取持仓: {len(active_positions)}个活跃持仓 (合约: {len(active_contract_positions)}, 现货: {len(spot_positions)})")
            
            return active_positions
        except Exception as e:
            self._positions_fetch_errors = getattr(self, '_positions_fetch_errors', 0) + 1
            logging.error(f"获取持仓失败(第{self._positions_fetch_errors}次): {str(e)}")
            
            # 如果有缓存且错误次数不多，返回缓存数据
            if (hasattr(self, '_positions_cache') and 
                self._positions_cache and 
                self._positions_fetch_errors <= 5 and
                time.time() - self._positions_cache_time < 60):  # 1分钟内的缓存仍可用
                logging.warning(f"使用缓存持仓数据，缓存时间: {time.time() - self._positions_cache_time:.1f}秒")
                return self._positions_cache.get('positions', [])
            
            return []

    def calculate_position_pnl(self, exchange, position):
        """
        计算持仓盈亏，支持现货和合约（优化版本）

        参数:
        - exchange: 交易所对象
        - position: 持仓信息

        返回:
        - pnl: 盈亏金额
        - pnl_percentage: 盈亏百分比
        """
        try:
            symbol = position['symbol']
            entry_price = float(position['entryPrice'])
            side = position['side']

            # 判断是现货还是合约
            is_spot = ":" not in symbol

            # 获取持仓数量（现货和合约的字段名不同）
            if is_spot:
                quantity = float(position.get('amount', 0))
            else:
                quantity = float(position.get('contracts', 0))

            # 优化：优先使用价格缓存，减少API调用
            current_price = None
            if hasattr(self, '_price_cache') and symbol in self._price_cache:
                cache_data = self._price_cache[symbol]
                cache_age = time.time() - cache_data['timestamp']
                if cache_age < 10:  # 10秒内的缓存有效
                    current_price = cache_data['price']
            
            # 缓存未命中时才调用API
            if current_price is None:
                ticker = exchange.fetch_ticker(symbol)
                current_price = float(ticker['last'])
                
                # 更新价格缓存
                if not hasattr(self, '_price_cache'):
                    self._price_cache = {}
                self._price_cache[symbol] = {
                    'price': current_price,
                    'timestamp': time.time()
                }

            # 优化：减少日志输出频率（每30秒记录一次详细信息）
            current_time = time.time()
            log_key = f"pnl_calc_{symbol}"
            if not hasattr(self, '_pnl_calc_log_times'):
                self._pnl_calc_log_times = {}
            
            should_log = (log_key not in self._pnl_calc_log_times or 
                         current_time - self._pnl_calc_log_times[log_key] > 30)
            
            if should_log:
                self._pnl_calc_log_times[log_key] = current_time
                logging.info(f"盈亏计算: {symbol}, 入场价: {entry_price}, 当前价: {current_price}, 方向: {side}")

            # 计算盈亏（优化计算逻辑）
            price_diff = current_price - entry_price
            
            if is_spot:
                # 现货只有多头
                pnl = price_diff * quantity
                pnl_percentage = (price_diff / entry_price) * 100
            else:
                # 合约可以做多做空
                if side == 'long':
                    pnl = price_diff * quantity
                    pnl_percentage = (price_diff / entry_price) * 100
                else:  # short
                    pnl = -price_diff * quantity
                    pnl_percentage = (-price_diff / entry_price) * 100

            # 优化：只在盈亏变化较大时记录详细结果
            if should_log or abs(pnl_percentage) > 5:  # 盈亏超过5%时记录
                logging.info(f"盈亏结果: {symbol}, 金额: {pnl:.4f}, 百分比: {pnl_percentage:.2f}%")

            return pnl, pnl_percentage
        except Exception as e:
            logging.error(f"计算持仓盈亏失败: {str(e)}")
            return 0, 0

    # 其余交易、策略、界面等方法可直接复用V1.374逻辑，略

def startup_self_check():
    """程序启动时的自动检测模块"""
    try:
        print("=" * 50)
        print("交易系统启动自检开始...")
        print("=" * 50)
        
        # 检查1：文件完整性
        print("[1/5] 检查文件完整性...")
        required_files = ['config.json']
        missing_files = []
        for file in required_files:
            if not os.path.exists(file):
                missing_files.append(file)
        
        if missing_files:
            print(f"   ⚠️  缺少文件: {', '.join(missing_files)}")
            print("   ✅ 将自动创建缺失的配置文件")
        else:
            print("   ✅ 所有必需文件完整")
        
        # 检查2：依赖库
        print("[2/5] 检查依赖库...")
        try:
            import ccxt
            import tkinter
            from cryptography.fernet import Fernet
            print("   ✅ 核心依赖库检查通过")
        except ImportError as e:
            print(f"   ❌ 依赖库缺失: {str(e)}")
            print("   请运行: pip install ccxt tkinter cryptography")
        
        # 检查3：网络连接
        print("[3/5] 检查网络连接...")
        try:
            import urllib.request
            urllib.request.urlopen('https://www.okx.com', timeout=5)
            print("   ✅ 网络连接正常")
        except:
            print("   ⚠️  网络连接可能存在问题")
        
        # 检查4：系统资源
        print("[4/5] 检查系统资源...")
        try:
            import psutil
            memory_percent = psutil.virtual_memory().percent
            cpu_percent = psutil.cpu_percent(interval=1)
            print(f"   ✅ 内存使用率: {memory_percent:.1f}%")
            print(f"   ✅ CPU使用率: {cpu_percent:.1f}%")
            if memory_percent > 90:
                print("   ⚠️  内存使用率较高，可能影响性能")
        except ImportError:
            print("   ⚠️  无法检查系统资源 (psutil未安装)")
        
        # 检查5：程序配置
        print("[5/5] 检查程序配置...")
        try:
            # 简单的配置文件格式检查
            if os.path.exists('config.json'):
                with open('config.json', 'r', encoding='utf-8') as f:
                    import json
                    config = json.load(f)
                    if isinstance(config, dict):
                        print("   ✅ 配置文件格式正确")
                    else:
                        print("   ⚠️  配置文件格式异常")
            else:
                print("   ✅ 将创建默认配置文件")
        except Exception as e:
            print(f"   ⚠️  配置文件检查失败: {str(e)}")
        
        print("=" * 50)
        print("自检完成，正在启动交易系统...")
        print("=" * 50)
        
        return True
        
    except Exception as e:
        print(f"自检过程发生错误: {str(e)}")
        print("将继续启动程序...")
        return False

def enhanced_startup():
    """增强版启动流程"""
    app = None
    try:
        # 执行启动自检
        startup_self_check()
        
        # 创建应用实例
        print("正在初始化交易系统...")
        app = TradingSystem()
        
        # 启动后自动执行的任务
        def post_startup_tasks():
            """启动后的自动任务（在主线程中执行）"""
            try:
                # 检查是否有保存的配置
                if hasattr(app, 'config') and app.config:
                    print("检测到已保存的配置")
                
                print("启动后任务完成")
                
            except Exception as e:
                print(f"启动后任务执行失败: {str(e)}")
        
        # 将启动后任务保存到app实例中，在GUI创建后执行
        app.post_startup_tasks = post_startup_tasks
        
        print("交易系统启动完成！")
        
        # 启动GUI界面
        print("正在启动GUI界面...")
        app.show_login_dialog()
        
    except Exception as e:
        print(f"启动过程发生错误: {str(e)}")
        print("尝试基础启动...")
        # 如果增强启动失败，先清理已创建的实例
        if app is not None:
            try:
                # 停止可能已启动的线程
                if hasattr(app, 'running'):
                    app.running = False
                # 清理资源
                if hasattr(app, '_active_threads'):
                    app._active_threads.clear()
            except Exception:
                pass
        
        # 回退到基础启动
        try:
            app = TradingSystem()
            app.show_login_dialog()
        except Exception as e2:
            print(f"基础启动也失败: {str(e2)}")
            print("程序无法启动，请检查环境配置")

if __name__ == "__main__":
    # 使用增强版启动流程
    enhanced_startup()
