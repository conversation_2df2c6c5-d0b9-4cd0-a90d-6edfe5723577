#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
策略核心模块 - 统一的策略管理、接口和适配器
整合了原strategy_manager.py、strategy_interface.py和strategy_adapter.py的功能
提供完整的策略配置管理、执行调度和性能监控功能
"""

import json
import time
import asyncio
import logging
from pathlib import Path
from typing import Dict, List, Any, Callable, Optional
from collections import defaultdict
from functools import wraps
import threading

class StrategyCore:
    """策略核心管理器 - 统一策略管理、接口和适配器功能"""
    
    def __init__(self, config_path: str = "strategy_config.json"):
        self.config_path = config_path
        self.config = {}
        self.performance_data = defaultdict(list)
        self.lock = threading.Lock()
        self.logger = logging.getLogger(__name__)
        
        # 策略适配器映射
        self.select_product_adapters = {}
        self.trading_adapters = {}
        
        # 初始化
        self._load_config()
        self._init_aliases()
        self._register_adapters()
    
    def _load_config(self):
        """加载策略配置文件"""
        try:
            if Path(self.config_path).exists():
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    self.config = json.load(f)
                self.logger.info(f"策略配置加载成功: {self.config_path}")
            else:
                self._create_default_config()
                self.logger.info("创建默认策略配置")
        except Exception as e:
            self.logger.error(f"加载策略配置失败: {e}")
            self._create_default_config()
    
    def _create_default_config(self):
        """创建默认配置"""
        self.config = {
            "strategies": {
                "select_product": {
                    "选品策略一": {
                        "enabled": True,
                        "function_name": "select_symbols_by_atr_adx",
                        "priority": 1,
                        "description": "基于ATR和ADX指标的选品策略",
                        "parameters": {
                            "atr_period": 14,
                            "min_adx": 25
                        }
                    },
                    "选品策略二": {
                        "enabled": True,
                        "function_name": "smart_select_symbols_v2",
                        "priority": 2,
                        "description": "智能综合评分选品策略",
                        "parameters": {
                            "max_symbols": 10,
                            "min_volume_24h": 1000000
                        }
                    }
                },
                "trading": {
                    "交易策略一": {
                        "enabled": True,
                        "function_name": "smart_trading_strategy_v2",
                        "priority": 1,
                        "description": "智能交易策略v2",
                        "parameters": {
                            "rsi_period": 14,
                            "bb_period": 20
                        }
                    }
                },
                "performance_monitoring": {
                    "enabled": True,
                    "max_records_per_strategy": 1000
                },
                "strategy_combination": {
                    "enabled": False,
                    "voting_method": "weighted",
                    "weights": {}
                }
            }
        }
        
        # 保存默认配置
        try:
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.logger.error(f"保存默认配置失败: {e}")
    
    def _init_aliases(self):
        """初始化策略别名映射（向后兼容）"""
        self.strategy_aliases = {
            # 选品策略别名
            "select_symbols_by_atr_adx": "选品策略一",
            "smart_select_symbols_v2": "选品策略二", 
            "select_positions_to_close": "选品策略三",
            "smart_select_symbols": "选品策略四",
            
            # 交易策略别名
            "calculate_indicators": "交易策略一",
            "adaptive_strategy": "交易策略二",
            "smart_trading_strategy": "交易策略三",
            "smart_stop_loss": "交易策略四"
        }
    
    def _register_adapters(self):
        """注册策略适配器"""
        # 选品策略适配器
        self.select_product_adapters = {
            "选品策略一": self._adapt_select_symbols_by_atr_adx,
            "选品策略二": self._adapt_smart_select_symbols_v2,
            "选品策略三": self._adapt_select_positions_to_close,
            "选品策略四": self._adapt_smart_select_symbols
        }
        
        # 交易策略适配器
        self.trading_adapters = {
            "交易策略一": self._adapt_calculate_indicators,
            "交易策略二": self._adapt_adaptive_strategy,
            "交易策略三": self._adapt_smart_trading_strategy,
            "交易策略四": self._adapt_smart_stop_loss
        }
    
    # ==================== 配置管理功能 ====================
    
    def get_strategy_config(self, strategy_type: str, strategy_name: str) -> Dict[str, Any]:
        """获取策略配置"""
        try:
            # 支持别名查找
            if strategy_name in self.strategy_aliases:
                strategy_name = self.strategy_aliases[strategy_name]
            
            strategies = self.config.get("strategies", {}).get(strategy_type, {})
            return strategies.get(strategy_name, {})
        except Exception as e:
            self.logger.error(f"获取策略配置失败: {strategy_type}.{strategy_name}, 错误: {e}")
            return {}
    
    def get_enabled_strategies(self, strategy_type: str) -> List[Dict[str, Any]]:
        """获取启用的策略列表，按优先级排序"""
        try:
            strategies = self.config.get("strategies", {}).get(strategy_type, {})
            enabled_strategies = []
            
            for name, config in strategies.items():
                if config.get("enabled", True):
                    enabled_strategies.append({
                        "name": name,
                        "function_name": config.get("function_name", ""),
                        "priority": config.get("priority", 999),
                        "parameters": config.get("parameters", {}),
                        "description": config.get("description", "")
                    })
            
            # 按优先级排序
            enabled_strategies.sort(key=lambda x: x["priority"])
            return enabled_strategies
        except Exception as e:
            self.logger.error(f"获取启用策略失败: {strategy_type}, 错误: {e}")
            return []
    
    def get_strategy_list(self, strategy_type: str) -> List[str]:
        """获取策略名称列表"""
        try:
            strategies = self.config.get("strategies", {}).get(strategy_type, {})
            return list(strategies.keys())
        except Exception as e:
            self.logger.error(f"获取策略列表失败: {e}")
            return []
    
    def update_strategy_config(self, strategy_type: str, strategy_name: str, 
                             config_updates: Dict[str, Any]) -> bool:
        """更新策略配置（热更新）"""
        try:
            with self.lock:
                if "strategies" not in self.config:
                    self.config["strategies"] = {}
                if strategy_type not in self.config["strategies"]:
                    self.config["strategies"][strategy_type] = {}
                if strategy_name not in self.config["strategies"][strategy_type]:
                    self.config["strategies"][strategy_type][strategy_name] = {}
                
                # 更新配置
                self.config["strategies"][strategy_type][strategy_name].update(config_updates)
                
                # 保存到文件
                with open(self.config_path, 'w', encoding='utf-8') as f:
                    json.dump(self.config, f, ensure_ascii=False, indent=2)
                
                return True
        except Exception as e:
            self.logger.error(f"更新策略配置失败: {e}")
            return False
    
    def get_strategy_performance(self, strategy_name, timeframe='24h'):
        """获取策略性能统计"""
        try:
            actual_name = self.strategy_aliases.get(strategy_name, strategy_name)
            
            # 从性能记录中获取数据
            if hasattr(self, 'performance_tracker') and actual_name in self.performance_tracker:
                performance_data = self.performance_tracker[actual_name]
                
                # 计算关键指标
                total_trades = len(performance_data.get('trades', []))
                win_rate = performance_data.get('win_rate', 0)
                avg_profit = performance_data.get('avg_profit', 0)
                max_drawdown = performance_data.get('max_drawdown', 0)
                
                return {
                    'strategy_name': actual_name,
                    'total_trades': total_trades,
                    'win_rate': win_rate,
                    'avg_profit': avg_profit,
                    'max_drawdown': max_drawdown,
                    'last_updated': performance_data.get('last_updated', time.time())
                }
            else:
                return {
                    'strategy_name': actual_name,
                    'total_trades': 0,
                    'win_rate': 0,
                    'avg_profit': 0,
                    'max_drawdown': 0,
                    'last_updated': time.time()
                }
        except Exception as e:
            self.logger.error(f"获取策略性能失败: {str(e)}")
            return None
    
    def update_strategy_performance(self, strategy_name, trade_result):
        """更新策略性能记录"""
        try:
            actual_name = self.strategy_aliases.get(strategy_name, strategy_name)
            
            if not hasattr(self, 'performance_tracker'):
                self.performance_tracker = {}
            
            if actual_name not in self.performance_tracker:
                self.performance_tracker[actual_name] = {
                    'trades': [],
                    'total_profit': 0,
                    'win_count': 0,
                    'loss_count': 0,
                    'max_drawdown': 0,
                    'peak_profit': 0
                }
            
            tracker = self.performance_tracker[actual_name]
            
            # 添加交易记录
            trade_record = {
                'timestamp': time.time(),
                'profit': trade_result.get('profit', 0),
                'signal': trade_result.get('signal', 'hold'),
                'confidence': trade_result.get('confidence', 0)
            }
            
            tracker['trades'].append(trade_record)
            tracker['total_profit'] += trade_record['profit']
            
            # 更新胜负统计
            if trade_record['profit'] > 0:
                tracker['win_count'] += 1
            elif trade_record['profit'] < 0:
                tracker['loss_count'] += 1
            
            # 计算胜率
            total_trades = tracker['win_count'] + tracker['loss_count']
            tracker['win_rate'] = tracker['win_count'] / total_trades if total_trades > 0 else 0
            
            # 计算平均收益
            tracker['avg_profit'] = tracker['total_profit'] / len(tracker['trades']) if tracker['trades'] else 0
            
            # 更新最大回撤
            if tracker['total_profit'] > tracker['peak_profit']:
                tracker['peak_profit'] = tracker['total_profit']
            
            current_drawdown = tracker['peak_profit'] - tracker['total_profit']
            if current_drawdown > tracker['max_drawdown']:
                tracker['max_drawdown'] = current_drawdown
            
            tracker['last_updated'] = time.time()
            
            # 保持最近1000条记录
            if len(tracker['trades']) > 1000:
                tracker['trades'] = tracker['trades'][-1000:]
            
            self.logger.info(f"策略性能已更新: {actual_name}, 胜率: {tracker['win_rate']:.2%}")
            
        except Exception as e:
            self.logger.error(f"更新策略性能失败: {str(e)}")
    
    def get_best_strategy(self, market_condition='normal'):
        """根据市场条件获取最佳策略"""
        try:
            if not hasattr(self, 'performance_tracker') or not self.performance_tracker:
                # 如果没有性能数据，返回默认策略
                return 'smart_trading_strategy_v2'
            
            best_strategy = None
            best_score = -float('inf')
            
            for strategy_name, performance in self.performance_tracker.items():
                if len(performance['trades']) < 10:  # 需要足够的交易样本
                    continue
                
                # 计算综合评分
                win_rate_score = performance['win_rate'] * 100
                profit_score = performance['avg_profit'] * 10
                drawdown_penalty = performance['max_drawdown'] * 5
                
                # 根据市场条件调整权重
                if market_condition == 'volatile':
                    # 波动市场更重视风险控制
                    total_score = win_rate_score * 0.4 + profit_score * 0.3 - drawdown_penalty * 0.3
                elif market_condition == 'trending':
                    # 趋势市场更重视收益
                    total_score = win_rate_score * 0.3 + profit_score * 0.5 - drawdown_penalty * 0.2
                else:
                    # 正常市场平衡考虑
                    total_score = win_rate_score * 0.4 + profit_score * 0.4 - drawdown_penalty * 0.2
                
                if total_score > best_score:
                    best_score = total_score
                    best_strategy = strategy_name
            
            return best_strategy or 'smart_trading_strategy_v2'
            
        except Exception as e:
            self.logger.error(f"获取最佳策略失败: {str(e)}")
            return 'smart_trading_strategy_v2'
    
    def optimize_strategy_parameters(self, strategy_name, market_data):
        """动态优化策略参数"""
        try:
            actual_name = self.strategy_aliases.get(strategy_name, strategy_name)
            
            # 分析市场特征
            market_volatility = self._calculate_market_volatility(market_data)
            market_trend = self._calculate_market_trend(market_data)
            
            # 根据市场特征调整参数
            strategy_type = None
            for stype in ['select_product', 'trading']:
                if actual_name in self.config.get('strategies', {}).get(stype, {}):
                    strategy_type = stype
                    break
            
            if not strategy_type:
                return False
            
            config = self.config['strategies'][strategy_type][actual_name]
            
            if market_volatility > 0.05:  # 高波动市场
                config.setdefault('parameters', {}).update({
                    'risk_level': 'low',
                    'stop_loss_multiplier': 1.5,
                    'take_profit_multiplier': 2.0
                })
            elif market_volatility < 0.02:  # 低波动市场
                config.setdefault('parameters', {}).update({
                    'risk_level': 'medium',
                    'stop_loss_multiplier': 2.0,
                    'take_profit_multiplier': 3.0
                })
            
            if abs(market_trend) > 0.1:  # 强趋势市场
                config.setdefault('parameters', {}).update({
                    'trend_following': True,
                    'signal_threshold': 0.6
                })
            else:  # 震荡市场
                config.setdefault('parameters', {}).update({
                    'trend_following': False,
                    'signal_threshold': 0.8
                })
            
            self.logger.info(f"策略参数已优化: {actual_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"优化策略参数失败: {str(e)}")
            return False
    
    def _calculate_market_volatility(self, market_data):
        """计算市场波动率"""
        try:
            if not market_data or len(market_data) < 20:
                return 0.03  # 默认波动率
            
            prices = [float(data.get('close', 0)) for data in market_data[-20:]]
            returns = []
            
            for i in range(1, len(prices)):
                if prices[i-1] > 0:
                    returns.append((prices[i] / prices[i-1]) - 1)
            
            if returns:
                # 简单标准差计算
                mean_return = sum(returns) / len(returns)
                variance = sum((r - mean_return) ** 2 for r in returns) / len(returns)
                return variance ** 0.5
            else:
                return 0.03
                
        except Exception:
            return 0.03
    
    def _calculate_market_trend(self, market_data):
        """计算市场趋势"""
        try:
            if not market_data or len(market_data) < 20:
                return 0
            
            prices = [float(data.get('close', 0)) for data in market_data[-20:]]
            
            if len(prices) >= 2 and prices[0] > 0:
                return (prices[-1] / prices[0]) - 1
            else:
                return 0
                
        except Exception:
            return 0
    
    # ==================== 性能监控功能 ====================
    
    def record_performance(self, strategy_type: str, strategy_name: str, 
                          execution_time: float, success: bool, 
                          profit_loss: float = 0.0, **kwargs):
        """记录策略性能数据"""
        if not self.config.get("strategies", {}).get("performance_monitoring", {}).get("enabled", True):
            return
        
        try:
            with self.lock:
                key = f"{strategy_type}.{strategy_name}"
                record = {
                    "timestamp": time.time(),
                    "execution_time": execution_time,
                    "success": success,
                    "profit_loss": profit_loss,
                    **kwargs
                }
                self.performance_data[key].append(record)
        except Exception as e:
            self.logger.error(f"记录性能数据失败: {e}")
    
    def get_performance_stats(self, strategy_type: str, strategy_name: str) -> Dict[str, Any]:
        """获取策略性能统计"""
        try:
            with self.lock:
                key = f"{strategy_type}.{strategy_name}"
                records = list(self.performance_data[key])
                
                if not records:
                    return {"total_calls": 0}
                
                total_calls = len(records)
                success_calls = sum(1 for r in records if r["success"])
                avg_execution_time = sum(r["execution_time"] for r in records) / total_calls
                total_profit_loss = sum(r["profit_loss"] for r in records)
                
                return {
                    "total_calls": total_calls,
                    "success_rate": success_calls / total_calls if total_calls > 0 else 0,
                    "avg_execution_time": avg_execution_time,
                    "total_profit_loss": total_profit_loss,
                    "last_call": records[-1]["timestamp"] if records else 0
                }
        except Exception as e:
            self.logger.error(f"获取性能统计失败: {e}")
            return {"total_calls": 0}
    
    def performance_monitor(self, strategy_type: str, strategy_name: str):
        """性能监控装饰器"""
        def decorator(func: Callable):
            @wraps(func)
            def wrapper(*args, **kwargs):
                start_time = time.time()
                success = False
                profit_loss = 0.0
                result = None
                
                try:
                    result = func(*args, **kwargs)
                    success = True
                    
                    # 尝试从结果中提取盈亏信息
                    if isinstance(result, dict) and "profit_loss" in result:
                        profit_loss = result["profit_loss"]
                    
                except Exception as e:
                    self.logger.error(f"策略执行失败: {strategy_type}.{strategy_name}, 错误: {e}")
                    # 返回策略特定的兜底结果
                    if strategy_type == "select_product":
                        result = ["BTC-USDT", "ETH-USDT"]  # 默认选品
                    elif strategy_type == "trading":
                        result = {"action": "hold", "reason": "策略执行失败"}  # 默认持有
                    else:
                        result = None
                finally:
                    execution_time = time.time() - start_time
                    self.record_performance(strategy_type, strategy_name, 
                                          execution_time, success, profit_loss)
                
                return result
            return wrapper
        return decorator
    
    # ==================== 策略组合功能 ====================
    
    def combine_strategies(self, strategy_type: str, results: List[Any]) -> Any:
        """策略组合管理器 - 使用加权投票机制"""
        if not self.config.get("strategies", {}).get("strategy_combination", {}).get("enabled", False):
            # 如果未启用组合，返回第一个有效结果
            return results[0] if results else None
        
        try:
            voting_method = self.config.get("strategies", {}).get("strategy_combination", {}).get("voting_method", "weighted")
            
            if voting_method == "weighted":
                return self._weighted_voting(strategy_type, results)
            elif voting_method == "majority":
                return self._majority_voting(results)
            else:
                return results[0] if results else None
        except Exception as e:
            self.logger.error(f"策略组合失败: {e}")
            return results[0] if results else None
    
    def _weighted_voting(self, strategy_type: str, results: List[Any]) -> Any:
        """加权投票机制"""
        weights = self.config.get("strategies", {}).get("strategy_combination", {}).get("weights", {})
        
        if strategy_type == "select_product":
            # 对于选品策略，合并所有结果并去重
            combined_symbols = set()
            for result in results:
                if isinstance(result, list):
                    combined_symbols.update(result)
            return list(combined_symbols)
        
        elif strategy_type == "trading":
            # 对于交易策略，使用加权平均
            if not results or not all(isinstance(r, dict) for r in results):
                return results[0] if results else {"action": "hold"}
            
            # 简化的加权投票：选择最常见的action
            actions = [r.get("action", "hold") for r in results]
            action_counts = defaultdict(int)
            for action in actions:
                action_counts[action] += 1
            
            best_action = max(action_counts.items(), key=lambda x: x[1])[0]
            return {"action": best_action, "reason": "策略组合决策"}
        
        return results[0] if results else None
    
    def _majority_voting(self, results: List[Any]) -> Any:
        """多数投票机制"""
        if not results:
            return None
        
        # 简化实现：返回最常见的结果
        result_counts = defaultdict(int)
        for result in results:
            result_str = str(result)
            result_counts[result_str] += 1
        
        best_result_str = max(result_counts.items(), key=lambda x: x[1])[0]
        
        # 找到对应的原始结果
        for result in results:
            if str(result) == best_result_str:
                return result
        
        return results[0]
    
    # ==================== 策略执行接口 ====================
    
    async def execute_select_product_strategies(self, exchange, symbols: List[str] = None, 
                                              strategy_names: List[str] = None,
                                              combine_results: bool = True) -> List[str]:
        """执行选品策略"""
        try:
            # 如果没有指定策略，获取所有启用的策略
            if strategy_names is None:
                enabled_strategies = self.get_enabled_strategies("select_product")
                strategy_names = [s["name"] for s in enabled_strategies]
            
            if not strategy_names:
                self.logger.warning("没有启用的选品策略")
                return ["BTC-USDT", "ETH-USDT"]
            
            # 执行所有策略
            results = []
            for strategy_name in strategy_names:
                try:
                    result = await self.call_select_product_strategy(
                        strategy_name, exchange, symbols
                    )
                    if result:
                        results.append(result)
                        self.logger.info(f"选品策略 {strategy_name} 执行成功: {len(result)} 个品种")
                except Exception as e:
                    self.logger.error(f"选品策略 {strategy_name} 执行失败: {e}")
            
            if not results:
                return ["BTC-USDT", "ETH-USDT"]
            
            # 组合结果
            if combine_results and len(results) > 1:
                combined = self.combine_strategies("select_product", results)
                return combined if combined else results[0]
            else:
                return results[0] if results else ["BTC-USDT", "ETH-USDT"]
        
        except Exception as e:
            self.logger.error(f"执行选品策略失败: {e}")
            return ["BTC-USDT", "ETH-USDT"]
    
    async def execute_trading_strategies(self, exchange, symbol: str,
                                       strategy_names: List[str] = None,
                                       combine_results: bool = True) -> Dict[str, Any]:
        """执行交易策略"""
        try:
            # 如果没有指定策略，获取所有启用的策略
            if strategy_names is None:
                enabled_strategies = self.get_enabled_strategies("trading")
                strategy_names = [s["name"] for s in enabled_strategies]
            
            if not strategy_names:
                self.logger.warning("没有启用的交易策略")
                return {"action": "hold", "reason": "没有启用的策略"}
            
            # 执行所有策略
            results = []
            for strategy_name in strategy_names:
                try:
                    result = await self.call_trading_strategy(
                        strategy_name, exchange, symbol
                    )
                    if result:
                        results.append(result)
                        self.logger.info(f"策略 {strategy_name} 执行成功: {result.get('action', 'unknown')}")
                except Exception as e:
                    self.logger.error(f"策略 {strategy_name} 执行失败: {e}")
            
            if not results:
                return {"action": "hold", "reason": "所有策略执行失败"}
            
            # 组合结果
            if combine_results and len(results) > 1:
                combined = self.combine_strategies("trading", results)
                return combined if combined else results[0]
            else:
                return results[0] if results else {"action": "hold", "reason": "策略执行失败"}
        
        except Exception as e:
            self.logger.error(f"执行交易策略失败: {e}")
            return {"action": "hold", "reason": f"策略执行异常: {e}"}
    
    # ==================== 策略适配器功能 ====================
    
    async def call_select_product_strategy(self, strategy_name: str, 
                                         exchange, symbols: List[str] = None, 
                                         **kwargs) -> List[str]:
        """调用选品策略的统一接口"""
        try:
            if strategy_name not in self.select_product_adapters:
                self.logger.error(f"未知的选品策略: {strategy_name}")
                return ["BTC-USDT", "ETH-USDT"]
            
            adapter = self.select_product_adapters[strategy_name]
            result = await adapter(exchange, symbols, **kwargs)
            
            # 确保返回格式一致
            if not isinstance(result, list):
                result = ["BTC-USDT", "ETH-USDT"]
            
            return result
        except Exception as e:
            self.logger.error(f"选品策略调用失败: {strategy_name}, 错误: {e}")
            return ["BTC-USDT", "ETH-USDT"]
    
    async def call_trading_strategy(self, strategy_name: str, 
                                  exchange, symbol: str, 
                                  **kwargs) -> Dict[str, Any]:
        """调用交易策略的统一接口"""
        try:
            if strategy_name not in self.trading_adapters:
                self.logger.error(f"未知的交易策略: {strategy_name}")
                return {"action": "hold", "reason": "未知策略"}
            
            adapter = self.trading_adapters[strategy_name]
            result = await adapter(exchange, symbol, **kwargs)
            
            # 确保返回格式一致
            if not isinstance(result, dict):
                result = {"action": "hold", "reason": "策略返回格式错误"}
            
            return result
        except Exception as e:
            self.logger.error(f"交易策略调用失败: {strategy_name}, 错误: {e}")
            return {"action": "hold", "reason": f"策略执行失败: {e}"}
    
    # ==================== 选品策略适配器 ====================
    
    async def _adapt_select_symbols_by_atr_adx(self, exchange, symbols: List[str], **kwargs):
        """适配选品策略一"""
        from Select_product import select_symbols_by_atr_adx
        
        # 获取策略配置
        config = self.get_strategy_config("select_product", "选品策略一")
        params = config.get("parameters", {})
        
        # 合并参数
        atr_threshold = kwargs.get("atr_threshold", params.get("atr_period", 14) * 0.001)
        adx_threshold = kwargs.get("adx_threshold", params.get("min_adx", 25))
        
        if symbols is None:
            symbols = ["BTC-USDT", "ETH-USDT", "BNB-USDT", "ADA-USDT", "SOL-USDT"]
        
        return await select_symbols_by_atr_adx(exchange, symbols, atr_threshold, adx_threshold)
    
    async def _adapt_smart_select_symbols_v2(self, exchange, symbols: List[str], **kwargs):
        """适配选品策略二"""
        from Select_product import smart_select_symbols_v2
        
        # 获取策略配置
        config = self.get_strategy_config("select_product", "选品策略二")
        params = config.get("parameters", {})
        
        # 合并参数
        limit = kwargs.get("limit", params.get("max_symbols", 10))
        min_volume_24h = kwargs.get("min_volume_24h", 1000000)
        min_price_change = kwargs.get("min_price_change", 0.02)
        max_symbols = kwargs.get("max_symbols", params.get("max_symbols", 15))
        
        return await smart_select_symbols_v2(exchange, symbols, limit, 
                                           min_volume_24h, min_price_change, max_symbols)
    
    async def _adapt_select_positions_to_close(self, exchange, symbols: List[str], **kwargs):
        """适配选品策略三"""
        from Select_product import select_positions_to_close
        
        # 获取策略配置
        config = self.get_strategy_config("select_product", "选品策略三")
        params = config.get("parameters", {})
        
        # 获取当前持仓（模拟）
        positions = kwargs.get("positions", [])
        max_close = kwargs.get("max_close", params.get("max_positions", 3))
        
        # 如果没有持仓信息，返回空列表
        if not positions:
            return []
        
        # 注意：这个函数可能不是异步的，需要适配
        if asyncio.iscoroutinefunction(select_positions_to_close):
            return await select_positions_to_close(positions, max_close)
        else:
            return select_positions_to_close(positions, max_close)
    
    async def _adapt_smart_select_symbols(self, exchange, symbols: List[str], **kwargs):
        """适配选品策略四"""
        from Select_product import smart_select_symbols
        
        limit = kwargs.get("limit", 10)
        
        return await smart_select_symbols(exchange, symbols, limit)
    
    # ==================== 交易策略适配器 ====================
    
    async def _adapt_calculate_indicators(self, exchange, symbol: str, **kwargs):
        """适配交易策略一"""
        from Trading_strategy import calculate_indicators
        
        # 获取策略配置
        config = self.get_strategy_config("trading", "交易策略一")
        params = config.get("parameters", {})
        
        try:
            # 获取K线数据
            timeframe = kwargs.get("timeframe", "1h")
            limit = kwargs.get("limit", 100)
            
            ohlcv = await exchange.fetch_ohlcv(symbol, timeframe, limit=limit)
            
            # 调用指标计算函数
            if asyncio.iscoroutinefunction(calculate_indicators):
                result = await calculate_indicators(ohlcv)
            else:
                result = calculate_indicators(ohlcv)
            
            # 转换为交易信号
            if isinstance(result, dict) and "close" in result:
                return {"action": "hold", "reason": "指标计算完成", "indicators": result}
            else:
                return {"action": "hold", "reason": "指标计算完成"}
        except Exception as e:
            return {"action": "hold", "reason": f"指标计算失败: {e}"}
    
    async def _adapt_adaptive_strategy(self, exchange, symbol: str, **kwargs):
        """适配交易策略二"""
        from Trading_strategy import adaptive_strategy
        
        try:
            # 调用自适应策略
            if asyncio.iscoroutinefunction(adaptive_strategy):
                result = await adaptive_strategy(exchange, symbol)
            else:
                result = adaptive_strategy(exchange, symbol)
            
            # 确保返回格式正确
            if isinstance(result, dict) and "action" in result:
                return result
            else:
                return {"action": "hold", "reason": "策略执行完成"}
        except Exception as e:
            return {"action": "hold", "reason": f"自适应策略失败: {e}"}
    
    async def _adapt_smart_trading_strategy(self, exchange, symbol: str, **kwargs):
        """适配交易策略三"""
        from Trading_strategy import smart_trading_strategy
        
        try:
            # 调用智能交易策略
            if asyncio.iscoroutinefunction(smart_trading_strategy):
                result = await smart_trading_strategy(exchange, symbol)
            else:
                result = smart_trading_strategy(exchange, symbol)
            
            # 确保返回格式正确
            if isinstance(result, dict) and "action" in result:
                return result
            else:
                return {"action": "hold", "reason": "策略执行完成"}
        except Exception as e:
            return {"action": "hold", "reason": f"智能交易策略失败: {e}"}
    
    async def _adapt_smart_stop_loss(self, exchange, symbol: str, **kwargs):
        """适配交易策略四"""
        from Trading_strategy import smart_stop_loss
        
        # 获取策略配置
        config = self.get_strategy_config("trading", "交易策略四")
        params = config.get("parameters", {})
        
        try:
            # 获取必要参数
            position = kwargs.get("position", {})
            take_profit = kwargs.get("take_profit", params.get("base_take_profit", 0.04) * 100)
            stop_loss = kwargs.get("stop_loss", -params.get("base_stop_loss", 0.02) * 100)
            
            # 调用止损策略
            if asyncio.iscoroutinefunction(smart_stop_loss):
                result = await smart_stop_loss(exchange, symbol, position, take_profit, stop_loss)
            else:
                result = smart_stop_loss(exchange, symbol, position, take_profit, stop_loss)
            
            # 确保返回格式正确
            if isinstance(result, dict):
                return result
            else:
                return {"action": "hold", "reason": "止损策略执行完成"}
        except Exception as e:
            return {"action": "hold", "reason": f"止损策略失败: {e}"}
    
    # ==================== 高级功能接口 ====================
    
    def get_strategy_performance_report(self) -> Dict[str, Any]:
        """获取策略性能报告"""
        try:
            report = {
                "select_product_strategies": {},
                "trading_strategies": {},
                "summary": {
                    "total_strategies": 0,
                    "active_strategies": 0,
                    "avg_success_rate": 0.0
                }
            }
            
            # 获取选品策略性能
            select_strategies = self.get_enabled_strategies("select_product")
            for strategy in select_strategies:
                stats = self.get_performance_stats("select_product", strategy["name"])
                report["select_product_strategies"][strategy["name"]] = {
                    "description": strategy["description"],
                    "enabled": True,
                    "stats": stats
                }
            
            # 获取交易策略性能
            trading_strategies = self.get_enabled_strategies("trading")
            for strategy in trading_strategies:
                stats = self.get_performance_stats("trading", strategy["name"])
                report["trading_strategies"][strategy["name"]] = {
                    "description": strategy["description"],
                    "enabled": True,
                    "stats": stats
                }
            
            # 计算汇总信息
            all_stats = []
            for category in ["select_product_strategies", "trading_strategies"]:
                for strategy_name, strategy_info in report[category].items():
                    stats = strategy_info["stats"]
                    if stats["total_calls"] > 0:
                        all_stats.append(stats)
            
            report["summary"]["total_strategies"] = len(select_strategies) + len(trading_strategies)
            report["summary"]["active_strategies"] = len(all_stats)
            
            if all_stats:
                avg_success_rate = sum(s["success_rate"] for s in all_stats) / len(all_stats)
                report["summary"]["avg_success_rate"] = avg_success_rate
            
            return report
        
        except Exception as e:
            self.logger.error(f"获取策略性能报告失败: {e}")
            return {"error": str(e)}
    
    def list_available_strategies(self) -> Dict[str, List[str]]:
        """列出所有可用策略"""
        try:
            return {
                "select_product": self.get_strategy_list("select_product"),
                "trading": self.get_strategy_list("trading")
            }
        except Exception as e:
            self.logger.error(f"获取策略列表失败: {e}")
            return {"select_product": [], "trading": []}
    
    def enable_strategy(self, strategy_type: str, strategy_name: str) -> bool:
        """启用策略"""
        try:
            return self.update_strategy_config(
                strategy_type, strategy_name, {"enabled": True}
            )
        except Exception as e:
            self.logger.error(f"启用策略失败: {e}")
            return False
    
    def disable_strategy(self, strategy_type: str, strategy_name: str) -> bool:
        """禁用策略"""
        try:
            return self.update_strategy_config(
                strategy_type, strategy_name, {"enabled": False}
            )
        except Exception as e:
            self.logger.error(f"禁用策略失败: {e}")
            return False
    
    def update_strategy_parameters(self, strategy_type: str, strategy_name: str, 
                                 parameters: Dict[str, Any]) -> bool:
        """更新策略参数"""
        try:
            return self.update_strategy_config(
                strategy_type, strategy_name, {"parameters": parameters}
            )
        except Exception as e:
            self.logger.error(f"更新策略参数失败: {e}")
            return False

# ==================== 全局实例和便捷函数 ====================

# 全局策略核心实例
strategy_core = StrategyCore()

# 向后兼容的便捷函数
def get_strategy_config(strategy_type: str, strategy_name: str) -> Dict[str, Any]:
    """获取策略配置的便捷函数"""
    return strategy_core.get_strategy_config(strategy_type, strategy_name)

def get_enabled_strategies(strategy_type: str) -> List[Dict[str, Any]]:
    """获取启用策略的便捷函数"""
    return strategy_core.get_enabled_strategies(strategy_type)

def performance_monitor(strategy_type: str, strategy_name: str):
    """性能监控装饰器的便捷函数"""
    return strategy_core.performance_monitor(strategy_type, strategy_name)

def get_performance_stats(strategy_type: str, strategy_name: str) -> Dict[str, Any]:
    """获取性能统计的便捷函数"""
    return strategy_core.get_performance_stats(strategy_type, strategy_name)

async def execute_select_product_strategies(exchange, symbols: List[str] = None, 
                                          strategy_names: List[str] = None,
                                          combine_results: bool = True) -> List[str]:
    """执行选品策略的便捷函数"""
    return await strategy_core.execute_select_product_strategies(
        exchange, symbols, strategy_names, combine_results
    )

async def execute_trading_strategies(exchange, symbol: str,
                                   strategy_names: List[str] = None,
                                   combine_results: bool = True) -> Dict[str, Any]:
    """执行交易策略的便捷函数"""
    return await strategy_core.execute_trading_strategies(
        exchange, symbol, strategy_names, combine_results
    )

async def call_select_product_strategy(strategy_name: str, exchange, symbols: List[str] = None, **kwargs) -> List[str]:
    """调用选品策略的便捷函数"""
    return await strategy_core.call_select_product_strategy(strategy_name, exchange, symbols, **kwargs)

async def call_trading_strategy(strategy_name: str, exchange, symbol: str, **kwargs) -> Dict[str, Any]:
    """调用交易策略的便捷函数"""
    return await strategy_core.call_trading_strategy(strategy_name, exchange, symbol, **kwargs)

def get_strategy_performance_report() -> Dict[str, Any]:
    """获取策略性能报告的便捷函数"""
    return strategy_core.get_strategy_performance_report()

def list_available_strategies() -> Dict[str, List[str]]:
    """列出可用策略的便捷函数"""
    return strategy_core.list_available_strategies()

# ==================== 缺失函数修复 ====================

def execute_select_product_strategy(exchange, symbols=None, limit=5, strategy_name=None, **kwargs):
    """执行选品策略的统一入口函数"""
    import asyncio
    
    try:
        # 获取启用的选品策略
        enabled_strategies = strategy_core.get_enabled_strategies('select_product')
        if not enabled_strategies:
            logging.warning("没有启用的选品策略，使用默认品种")
            return ["BTC-USDT", "ETH-USDT"], "default"
        
        # 如果指定了策略名称，使用指定策略
        if strategy_name:
            target_strategy = None
            for strategy in enabled_strategies:
                if strategy['name'] == strategy_name:
                    target_strategy = strategy
                    break
            if not target_strategy:
                logging.warning(f"指定的策略 {strategy_name} 未找到，使用第一个启用的策略")
                target_strategy = enabled_strategies[0]
        else:
            # 使用优先级最高的策略
            target_strategy = min(enabled_strategies, key=lambda x: x.get('priority', 999))
        
        # 调用策略函数
        func_name = target_strategy.get('function_name')
        strategy_params = target_strategy.get('parameters', {})
        
        # 将limit参数传递给策略
        if limit:
            strategy_params['limit'] = limit
        
        # 合并其他参数
        strategy_params.update(kwargs)
        
        # 获取可用交易对
        if symbols is None:
            try:
                if not hasattr(exchange, 'markets') or not exchange.markets:
                    exchange.load_markets()
                symbols = list(exchange.markets.keys())
            except Exception as e:
                logging.warning(f"获取交易对列表失败: {e}，使用配置的降级列表")
                # 尝试从配置文件获取降级符号
                try:
                    import json
                    with open('config.json', 'r', encoding='utf-8') as f:
                        config = json.load(f)
                    symbols = config.get('fallback_symbols', ["BTC/USDT:USDT", "ETH/USDT:USDT", "BNB/USDT:USDT"])
                except:
                    symbols = ["BTC/USDT:USDT", "ETH/USDT:USDT", "BNB/USDT:USDT"]
        
        # 调用异步策略函数
        if func_name == 'select_symbols_by_atr_adx':
            from Select_product import select_symbols_by_atr_adx
            # 运行异步函数
            try:
                # 安全的异步函数调用方式
                import concurrent.futures
                import threading
                
                def run_async_in_thread():
                    try:
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)
                        return loop.run_until_complete(select_symbols_by_atr_adx(exchange, symbols, **strategy_params))
                    finally:
                        loop.close()
                
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(run_async_in_thread)
                    result_tuple = future.result(timeout=30)
                    result = result_tuple[0] if isinstance(result_tuple, tuple) else result_tuple
            except Exception as e:
                logging.error(f"执行ATR/ADX策略失败: {e}")
                # 使用配置化的降级候选
                try:
                    with open('config.json', 'r', encoding='utf-8') as f:
                        config = json.load(f)
                    result = config.get('fallback_symbols', ["BTC/USDT:USDT", "ETH/USDT:USDT", "BNB/USDT:USDT"])
                except:
                    result = ["BTC/USDT:USDT", "ETH/USDT:USDT", "BNB/USDT:USDT"]
                
        elif func_name == 'smart_select_symbols_v2':
            from Select_product import smart_select_symbols_v2
            # 运行异步函数
            try:
                # 安全的异步函数调用方式
                import concurrent.futures
                import threading
                
                def run_async_in_thread():
                    try:
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)
                        return loop.run_until_complete(smart_select_symbols_v2(exchange, symbols, **strategy_params))
                    finally:
                        loop.close()
                
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(run_async_in_thread)
                    result_tuple = future.result(timeout=30)
                    result = result_tuple[0] if isinstance(result_tuple, tuple) else result_tuple
            except Exception as e:
                logging.error(f"执行智能选品策略失败: {e}")
                # 使用配置化的降级候选
                try:
                    with open('config.json', 'r', encoding='utf-8') as f:
                        config = json.load(f)
                    result = config.get('fallback_symbols', ["BTC/USDT:USDT", "ETH/USDT:USDT", "BNB/USDT:USDT"])
                except:
                    result = ["BTC/USDT:USDT", "ETH/USDT:USDT", "BNB/USDT:USDT"]
        else:
            logging.warning(f"未知的选品策略函数: {func_name}")
            result = ["BTC-USDT", "ETH-USDT"]
        
        # 确保返回列表格式
        if not isinstance(result, list):
            result = ["BTC-USDT", "ETH-USDT"]
        
        # 限制返回数量
        if limit and len(result) > limit:
            result = result[:limit]
        
        return result, target_strategy['name']
        
    except Exception as e:
        logging.error(f"执行选品策略失败: {e}")
        return ["BTC-USDT", "ETH-USDT"], "error_fallback"

def execute_trading_strategy(exchange, symbol, existing_positions=None, strategy_name=None, **kwargs):
    """执行交易策略的统一入口函数"""
    try:
        # 获取启用的交易策略
        enabled_strategies = strategy_core.get_enabled_strategies('trading')
        if not enabled_strategies:
            logging.warning("没有启用的交易策略，返回持有信号")
            return {
                'signal': 'hold',
                'quantity': 0,
                'reason': '没有启用的交易策略',
                'price': None,
                'strategy_name': 'default_hold'
            }
        
        # 如果指定了策略名称，使用指定策略
        if strategy_name:
            target_strategy = None
            for strategy in enabled_strategies:
                if strategy['name'] == strategy_name:
                    target_strategy = strategy
                    break
            if not target_strategy:
                logging.warning(f"指定的策略 {strategy_name} 未找到，使用第一个启用的策略")
                target_strategy = enabled_strategies[0]
        else:
            # 使用优先级最高的策略
            target_strategy = min(enabled_strategies, key=lambda x: x.get('priority', 999))
        
        # 调用策略函数
        func_name = target_strategy.get('function_name')
        strategy_params = target_strategy.get('parameters', {})
        strategy_params.update(kwargs)
        
        if func_name == 'smart_trading_strategy_v2':
            from Trading_strategy import smart_trading_strategy_v2
            result = smart_trading_strategy_v2(
                exchange=exchange,
                symbol=symbol,
                existing_positions=existing_positions,
                strategy_params=strategy_params,
                **kwargs
            )
        elif func_name == 'adaptive_strategy':
            from Trading_strategy import adaptive_strategy
            result = adaptive_strategy(exchange, symbol)
        elif func_name == 'smart_trading_strategy':
            from Trading_strategy import smart_trading_strategy
            result = smart_trading_strategy(exchange, symbol)
        else:
            logging.warning(f"未知的交易策略函数: {func_name}")
            result = {
                'signal': 'hold',
                'quantity': 0,
                'reason': f'未知策略函数: {func_name}',
                'price': None,
                'strategy_name': 'unknown_strategy'
            }
        
        # 确保返回格式正确
        if not isinstance(result, dict):
            result = {
                'signal': 'hold',
                'quantity': 0,
                'reason': '策略返回格式错误',
                'price': None,
                'strategy_name': target_strategy['name']
            }
        else:
            # 确保包含strategy_name
            if 'strategy_name' not in result:
                result['strategy_name'] = target_strategy['name']
        
        return result
        
    except Exception as e:
        logging.error(f"执行交易策略失败: {e}")
        return {
            'signal': 'hold',
            'quantity': 0,
            'reason': f'策略执行失败: {str(e)}',
            'price': None,
            'strategy_name': 'error_fallback'
        }

# 为了向后兼容，创建别名
strategy_manager = strategy_core
strategy_interface = strategy_core
strategy_adapter = strategy_core

if __name__ == "__main__":
    # 测试代码
    print("策略核心模块加载成功")
    print(f"可用选品策略: {strategy_core.get_strategy_list('select_product')}")
    print(f"可用交易策略: {strategy_core.get_strategy_list('trading')}")