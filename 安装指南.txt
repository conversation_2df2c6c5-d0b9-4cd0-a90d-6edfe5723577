欧易智能全自动交易系统 V1.383 - 安装指南
=======================================

一、系统要求
-----------
1. Windows 7/8/10/11 操作系统
2. Python 3.8 或更高版本
3. 稳定的网络连接（可能需要代理才能连接到OKX交易所）

二、安装步骤
-----------
1. 安装Python
   - 访问 https://www.python.org/downloads/ 下载最新版本的Python
   - 安装时勾选"Add Python to PATH"选项
   - 完成安装后，打开命令提示符，输入"python --version"验证安装

将所有程序文件复制到新环境
运行install_dependencies.bat安装所需依赖
运行check_system.bat验证系统环境
运行run_trading_system_v1.383.bat启动交易系统
这些脚本会自动检测环境并安装所需的依赖库，使您能够轻松地在新环境中部署交易系统。

3. 配置OKX API密钥
   - 登录OKX交易所网站
   - 进入"账户中心" -> "API管理"
   - 创建新的API密钥（确保选择正确的权限，至少需要"查询"和"交易"权限）
   - 记下API密钥、Secret Key和Passphrase

4. 启动程序
   - 双击运行"run_trading_system_v1.383.bat"
   - 在登录界面输入您的API密钥信息
   - 如果需要使用代理，请勾选"使用代理"选项

三、常见问题解决
--------------
1. Python安装问题
   - 确保安装时勾选了"Add Python to PATH"
   - 如果命令行无法识别python命令，可能需要手动添加Python到系统环境变量

2. 依赖安装失败
   - 尝试以管理员身份运行install_dependencies.bat
   - 如果某些包安装失败，可以手动安装：
     pip install ccxt>=4.0.104 numpy>=1.20.0 pandas>=1.3.0 matplotlib>=3.4.0 cryptography>=36.0.0 requests>=2.25.0

3. 网络连接问题
   - 如果无法连接到OKX交易所，请确保配置了正确的代理
   - 默认代理设置为127.0.0.1:10808，如需修改，请在登录界面手动调整

4. API密钥问题
   - 确保API密钥具有足够的权限
   - 对于模拟交易，请确保使用模拟交易的API密钥
   - 如果API验证失败，请在OKX网站上重新生成API密钥

5. 程序崩溃或异常
   - 查看trading_system.log文件了解详细错误信息
   - 确保所有依赖库都已正确安装并且版本符合要求

四、文件说明
-----------
1. trading_system_v1.383.py - 主程序文件
2. contract_symbols.py - 合约交易品种配置文件
3. spot_symbols.py - 现货交易品种配置文件
4. strategies.py - 交易策略配置文件
5. config.json - 主配置文件（API密钥等）
6. user_config.json - 用户账号及界面参数配置
7. encryption_key.key - 本地加密密钥
8. run_trading_system_v1.383.bat - 一键启动脚本
9. install_dependencies.bat - 依赖安装脚本
10. trading_system.log - 日志文件

五、联系与支持
------------
如遇到无法解决的问题，请联系技术支持。

注意：本程序仅用于模拟交易，不建议用于实盘交易。使用本程序进行交易操作风险自负。
