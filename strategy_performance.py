#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
策略性能监控和报告模块
提供策略执行性能的统计、分析和报告功能
"""

import time
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from collections import defaultdict, deque
import threading

class StrategyPerformanceMonitor:
    """策略性能监控器"""
    
    def __init__(self, max_records: int = 10000):
        self.max_records = max_records
        self.performance_data = defaultdict(lambda: {
            'execution_times': deque(maxlen=max_records),
            'success_count': 0,
            'error_count': 0,
            'total_calls': 0,
            'last_execution': None,
            'average_time': 0.0,
            'min_time': float('inf'),
            'max_time': 0.0,
            'recent_errors': deque(maxlen=100),
            'hourly_stats': defaultdict(int),
            'daily_stats': defaultdict(int)
        })
        self._lock = threading.RLock()
        
    def record_execution(self, strategy_name: str, execution_time: float, 
                        success: bool = True, error_msg: str = None):
        """记录策略执行性能"""
        with self._lock:
            data = self.performance_data[strategy_name]
            current_time = datetime.now()
            
            # 基本统计
            data['execution_times'].append(execution_time)
            data['total_calls'] += 1
            data['last_execution'] = current_time.isoformat()
            
            if success:
                data['success_count'] += 1
            else:
                data['error_count'] += 1
                if error_msg:
                    data['recent_errors'].append({
                        'time': current_time.isoformat(),
                        'error': error_msg
                    })
            
            # 时间统计
            data['min_time'] = min(data['min_time'], execution_time)
            data['max_time'] = max(data['max_time'], execution_time)
            data['average_time'] = sum(data['execution_times']) / len(data['execution_times'])
            
            # 时间段统计
            hour_key = current_time.strftime('%Y-%m-%d-%H')
            day_key = current_time.strftime('%Y-%m-%d')
            data['hourly_stats'][hour_key] += 1
            data['daily_stats'][day_key] += 1
            
    def get_strategy_performance(self, strategy_name: str) -> Dict[str, Any]:
        """获取指定策略的性能数据"""
        with self._lock:
            if strategy_name not in self.performance_data:
                return {}
                
            data = self.performance_data[strategy_name].copy()
            
            # 转换deque为list以便JSON序列化
            data['execution_times'] = list(data['execution_times'])
            data['recent_errors'] = list(data['recent_errors'])
            data['hourly_stats'] = dict(data['hourly_stats'])
            data['daily_stats'] = dict(data['daily_stats'])
            
            # 计算成功率
            if data['total_calls'] > 0:
                data['success_rate'] = data['success_count'] / data['total_calls']
                data['error_rate'] = data['error_count'] / data['total_calls']
            else:
                data['success_rate'] = 0.0
                data['error_rate'] = 0.0
                
            return data
    
    def get_all_performance(self) -> Dict[str, Dict[str, Any]]:
        """获取所有策略的性能数据"""
        result = {}
        for strategy_name in self.performance_data.keys():
            result[strategy_name] = self.get_strategy_performance(strategy_name)
        return result
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能摘要报告"""
        with self._lock:
            summary = {
                'total_strategies': len(self.performance_data),
                'strategies': {},
                'overall_stats': {
                    'total_calls': 0,
                    'total_successes': 0,
                    'total_errors': 0,
                    'average_execution_time': 0.0
                },
                'generated_at': datetime.now().isoformat()
            }
            
            total_time = 0
            total_executions = 0
            
            for strategy_name, data in self.performance_data.items():
                strategy_summary = {
                    'total_calls': data['total_calls'],
                    'success_count': data['success_count'],
                    'error_count': data['error_count'],
                    'success_rate': data['success_count'] / max(data['total_calls'], 1),
                    'average_time': data['average_time'],
                    'min_time': data['min_time'] if data['min_time'] != float('inf') else 0,
                    'max_time': data['max_time'],
                    'last_execution': data['last_execution'],
                    'recent_error_count': len(data['recent_errors'])
                }
                
                summary['strategies'][strategy_name] = strategy_summary
                
                # 累计总体统计
                summary['overall_stats']['total_calls'] += data['total_calls']
                summary['overall_stats']['total_successes'] += data['success_count']
                summary['overall_stats']['total_errors'] += data['error_count']
                
                if data['execution_times']:
                    total_time += sum(data['execution_times'])
                    total_executions += len(data['execution_times'])
            
            # 计算总体平均执行时间
            if total_executions > 0:
                summary['overall_stats']['average_execution_time'] = total_time / total_executions
            
            return summary
    
    def get_all_performance_data(self) -> Dict[str, Any]:
        """获取所有策略的性能数据"""
        with self._lock:
            result = {}
            for strategy_name, data in self.performance_data.items():
                result[strategy_name] = {
                    'total_calls': data['total_calls'],
                    'success_count': data['success_count'],
                    'error_count': data['error_count'],
                    'success_rate': data['success_count'] / max(data['total_calls'], 1),
                    'average_time': data['average_time'],
                    'min_time': data['min_time'] if data['min_time'] != float('inf') else 0,
                    'max_time': data['max_time'],
                    'last_execution': data['last_execution'],
                    'recent_error_count': len(data['recent_errors']),
                    'execution_times': list(data['execution_times']),
                    'recent_errors': list(data['recent_errors']),
                    'hourly_stats': dict(data['hourly_stats']),
                    'daily_stats': dict(data['daily_stats'])
                }
            return result
    
    def clear_all_data(self):
        """清除所有性能监控数据"""
        with self._lock:
            self.performance_data.clear()
    
    def cleanup_old_data(self, days_to_keep: int = 7):
        """清理旧的性能数据"""
        cutoff_date = datetime.now() - timedelta(days=days_to_keep)
        cutoff_str = cutoff_date.strftime('%Y-%m-%d')
        
        with self._lock:
            for strategy_name, data in self.performance_data.items():
                # 清理旧的小时统计
                old_hours = [k for k in data['hourly_stats'].keys() if k < cutoff_str]
                for hour_key in old_hours:
                    del data['hourly_stats'][hour_key]
                
                # 清理旧的日统计
                old_days = [k for k in data['daily_stats'].keys() if k < cutoff_str]
                for day_key in old_days:
                    del data['daily_stats'][day_key]
                    
                logging.info(f"清理了策略 {strategy_name} 的 {len(old_hours)} 个小时和 {len(old_days)} 个日期的旧数据")
    
    def export_performance_report(self, file_path: str = None) -> str:
        """导出性能报告到文件"""
        if file_path is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            file_path = f"strategy_performance_report_{timestamp}.json"
        
        report_data = {
            'report_info': {
                'generated_at': datetime.now().isoformat(),
                'report_type': 'strategy_performance',
                'version': '1.0'
            },
            'summary': self.get_performance_summary(),
            'detailed_data': self.get_all_performance()
        }
        
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, indent=2, ensure_ascii=False)
            logging.info(f"性能报告已导出到: {file_path}")
            return file_path
        except Exception as e:
            logging.error(f"导出性能报告失败: {str(e)}")
            raise
    
    def get_top_performers(self, metric: str = 'success_rate', limit: int = 5) -> List[Dict[str, Any]]:
        """获取表现最佳的策略"""
        valid_metrics = ['success_rate', 'average_time', 'total_calls']
        if metric not in valid_metrics:
            raise ValueError(f"无效的指标: {metric}，支持的指标: {valid_metrics}")
        
        strategies = []
        for strategy_name, data in self.performance_data.items():
            if data['total_calls'] == 0:
                continue
                
            strategy_info = {
                'name': strategy_name,
                'success_rate': data['success_count'] / data['total_calls'],
                'average_time': data['average_time'],
                'total_calls': data['total_calls']
            }
            strategies.append(strategy_info)
        
        # 根据指标排序
        reverse = metric != 'average_time'  # 执行时间越短越好
        strategies.sort(key=lambda x: x[metric], reverse=reverse)
        
        return strategies[:limit]

# 全局性能监控器实例
performance_monitor = StrategyPerformanceMonitor()

# 便捷函数
def record_strategy_performance(strategy_name: str, execution_time: float, 
                              success: bool = True, error_msg: str = None):
    """记录策略性能的便捷函数"""
    performance_monitor.record_execution(strategy_name, execution_time, success, error_msg)

def get_strategy_performance_report(strategy_name: str = None) -> Dict[str, Any]:
    """获取策略性能报告的便捷函数"""
    if strategy_name:
        return performance_monitor.get_strategy_performance(strategy_name)
    else:
        return performance_monitor.get_performance_summary()

def export_performance_report(file_path: str = None) -> str:
    """导出性能报告的便捷函数"""
    return performance_monitor.export_performance_report(file_path)

def cleanup_old_performance_data(days_to_keep: int = 7):
    """清理旧性能数据的便捷函数"""
    performance_monitor.cleanup_old_data(days_to_keep)

def get_top_performing_strategies(metric: str = 'success_rate', limit: int = 5) -> List[Dict[str, Any]]:
    """获取表现最佳策略的便捷函数"""
    return performance_monitor.get_top_performers(metric, limit)